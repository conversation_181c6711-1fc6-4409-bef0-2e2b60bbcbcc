/**
 * Testes para o PIX Payload Generator
 * 
 * Execute com: npx jest pix-payload-generator.test.ts
 * ou: npx ts-node pix-payload-generator.test.ts (para execução simples)
 */

const {
  generatePixPayload,
  createPixPayload
} = require('./pix-payload-generator.js');

// ============================================================================
// FUNÇÕES DE TESTE SIMPLES (caso não tenha Jest)
// ============================================================================

function assert(condition: boolean, message: string): void {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`);
  }
}

function assertThrows(fn: () => void, expectedMessage?: string): void {
  try {
    fn();
    throw new Error('Expected function to throw an error');
  } catch (error: any) {
    if (expectedMessage && !error.message.includes(expectedMessage)) {
      throw new Error(`Expected error message to contain "${expectedMessage}", but got: ${error.message}`);
    }
  }
}

// ============================================================================
// TESTES DE VALIDAÇÃO
// ============================================================================

function testValidations(): void {
  console.log('🧪 Testando validações...');
  
  // Teste 1: Configuração válida básica
  try {
    const payload = generatePixPayload({
      keyType: 'RANDOM',
      key: 'test-key',
      nameReceiver: 'Test User',
      cityReceiver: 'Test City'
    });
    assert(payload.length > 0, 'Payload deve ser gerado');
    assert(payload.startsWith('00020101'), 'Payload deve começar com formato correto');
    assert(/[0-9A-F]{4}$/.test(payload), 'Payload deve terminar com CRC válido');
    console.log('✅ Configuração básica válida');
  } catch (error: any) {
    console.error('❌ Erro na configuração básica:', error.message);
  }
  
  // Teste 2: Nome muito longo
  try {
    assertThrows(() => {
      generatePixPayload({
        keyType: 'RANDOM',
        key: 'test-key',
        nameReceiver: 'Nome extremamente longo que excede o limite de 25 caracteres',
        cityReceiver: 'City'
      });
    }, 'máximo 25 caracteres');
    console.log('✅ Validação de nome longo funcionando');
  } catch (error: any) {
    console.error('❌ Erro na validação de nome longo:', error.message);
  }

  // Teste 3: Cidade muito longa
  try {
    assertThrows(() => {
      generatePixPayload({
        keyType: 'RANDOM',
        key: 'test-key',
        nameReceiver: 'Test',
        cityReceiver: 'Cidade com nome muito longo que excede limite'
      });
    }, 'máximo 15 caracteres');
    console.log('✅ Validação de cidade longa funcionando');
  } catch (error: any) {
    console.error('❌ Erro na validação de cidade longa:', error.message);
  }

  // Teste 4: Sem chave nem URL
  try {
    assertThrows(() => {
      generatePixPayload({
        keyType: 'RANDOM',
        key: '',
        nameReceiver: 'Test',
        cityReceiver: 'City'
      });
    }, 'chave PIX ou URL');
    console.log('✅ Validação de chave obrigatória funcionando');
  } catch (error: any) {
    console.error('❌ Erro na validação de chave obrigatória:', error.message);
  }
}

// ============================================================================
// TESTES DE TIPOS DE CHAVE
// ============================================================================

function testKeyTypes(): void {
  console.log('\n🔑 Testando tipos de chave...');
  
  // Teste CPF válido
  try {
    const payload = generatePixPayload({
      keyType: 'CPF',
      key: '12345678909', // CPF válido para teste
      nameReceiver: 'Test',
      cityReceiver: 'City'
    });
    assert(payload.includes('12345678909'), 'CPF deve estar no payload');
    console.log('✅ CPF válido processado');
  } catch (error: any) {
    console.error('❌ Erro no CPF válido:', error.message);
  }

  // Teste telefone
  try {
    const payload = generatePixPayload({
      keyType: 'PHONE',
      key: '11999887766',
      nameReceiver: 'Test',
      cityReceiver: 'City'
    });
    assert(payload.includes('+5511999887766'), 'Telefone deve ser formatado com +55');
    console.log('✅ Telefone formatado corretamente');
  } catch (error: any) {
    console.error('❌ Erro no telefone:', error.message);
  }

  // Teste email
  try {
    const payload = generatePixPayload({
      keyType: 'EMAIL',
      key: '<EMAIL>',
      nameReceiver: 'Test',
      cityReceiver: 'City'
    });
    assert(payload.includes('<EMAIL>'), 'Email deve estar no payload');
    console.log('✅ Email processado');
  } catch (error: any) {
    console.error('❌ Erro no email:', error.message);
  }

  // Teste chave aleatória
  try {
    const randomKey = 'b5fe1edc-d108-410f-b966-eccaaca75e4f';
    const payload = generatePixPayload({
      keyType: 'RANDOM',
      key: randomKey,
      nameReceiver: 'Test',
      cityReceiver: 'City'
    });
    assert(payload.includes(randomKey), 'Chave aleatória deve estar no payload');
    console.log('✅ Chave aleatória processada');
  } catch (error: any) {
    console.error('❌ Erro na chave aleatória:', error.message);
  }
}

// ============================================================================
// TESTES DE FORMATAÇÃO
// ============================================================================

function testFormatting(): void {
  console.log('\n🎨 Testando formatação...');
  
  // Teste formatação de texto com acentos
  try {
    const payload = generatePixPayload({
      keyType: 'RANDOM',
      key: 'test-key',
      nameReceiver: 'José da Silva',
      cityReceiver: 'São Paulo',
      description: 'Pagamento à vista'
    });
    assert(payload.includes('Jose da Silva'), 'Acentos devem ser removidos do nome');
    assert(payload.includes('Sao Paulo'), 'Acentos devem ser removidos da cidade');
    console.log('✅ Formatação de acentos funcionando');
  } catch (error: any) {
    console.error('❌ Erro na formatação de acentos:', error.message);
  }

  // Teste valor com decimais
  try {
    const payload = generatePixPayload({
      keyType: 'RANDOM',
      key: 'test-key',
      nameReceiver: 'Test',
      cityReceiver: 'City',
      amount: 123.45
    });
    assert(payload.includes('123.45'), 'Valor deve estar formatado corretamente');
    console.log('✅ Formatação de valor funcionando');
  } catch (error: any) {
    console.error('❌ Erro na formatação de valor:', error.message);
  }
}

// ============================================================================
// TESTES DE ESTRUTURA DO PAYLOAD
// ============================================================================

function testPayloadStructure(): void {
  console.log('\n🏗️ Testando estrutura do payload...');
  
  try {
    const payload = generatePixPayload({
      keyType: 'RANDOM',
      key: 'test-key',
      nameReceiver: 'Test User',
      cityReceiver: 'Test City',
      amount: 100.00,
      identification: 'TEST001',
      description: 'Test payment'
    });
    
    // Verificações de estrutura
    assert(payload.startsWith('00020101'), 'Deve começar com formato de payload correto');
    assert(payload.includes('5802BR'), 'Deve conter código do país Brasil');
    assert(payload.includes('53039865'), 'Deve conter código da moeda BRL');
    assert(/6304[0-9A-F]{4}$/.test(payload), 'Deve terminar com CRC válido');
    
    console.log('✅ Estrutura do payload correta');
    console.log(`   Tamanho: ${payload.length} caracteres`);
  } catch (error: any) {
    console.error('❌ Erro na estrutura do payload:', error.message);
  }
}

// ============================================================================
// TESTES DE PIX DINÂMICO
// ============================================================================

function testDynamicPix(): void {
  console.log('\n🔄 Testando PIX dinâmico...');
  
  try {
    const payload = generatePixPayload({
      keyType: 'RANDOM', // Será ignorado
      key: '', // Será ignorado
      dynamicUrl: 'psp.example.com/payment/123',
      nameReceiver: 'Test Merchant',
      cityReceiver: 'Test City',
      singleTransaction: true
    });
    
    assert(payload.includes('010212'), 'Deve ter método de iniciação dinâmico');
    assert(payload.includes('psp.example.com/payment/123'), 'Deve conter URL dinâmica');
    
    console.log('✅ PIX dinâmico funcionando');
  } catch (error: any) {
    console.error('❌ Erro no PIX dinâmico:', error.message);
  }
}

// ============================================================================
// TESTES DE FUNÇÕES DE CONVENIÊNCIA
// ============================================================================

function testConvenienceFunctions(): void {
  console.log('\n🛠️ Testando funções de conveniência...');
  
  try {
    // Teste createPixPayload
    const payload1 = createPixPayload(
      'EMAIL',
      '<EMAIL>',
      'Test User',
      'Test City',
      50.00,
      'TXN001',
      'Test payment'
    );
    
    assert(payload1.length > 0, 'createPixPayload deve gerar payload');
    assert(payload1.includes('<EMAIL>'), 'Deve conter email');
    assert(payload1.includes('50.00'), 'Deve conter valor');
    
    // Teste generatePixPayload
    const payload2 = generatePixPayload({
      keyType: 'PHONE',
      key: '11999887766',
      nameReceiver: 'Test User',
      cityReceiver: 'Test City'
    });
    
    assert(payload2.length > 0, 'generatePixPayload deve gerar payload');
    assert(payload2.includes('+5511999887766'), 'Deve conter telefone formatado');
    
    console.log('✅ Funções de conveniência funcionando');
  } catch (error: any) {
    console.error('❌ Erro nas funções de conveniência:', error.message);
  }
}

// ============================================================================
// EXECUÇÃO DOS TESTES
// ============================================================================

function runAllTests(): void {
  console.log('🚀 Iniciando testes do PIX Payload Generator\n');
  
  try {
    testValidations();
    testKeyTypes();
    testFormatting();
    testPayloadStructure();
    testDynamicPix();
    testConvenienceFunctions();
    
    console.log('\n🎉 Todos os testes passaram com sucesso!');
  } catch (error: any) {
    console.error('\n💥 Erro durante execução dos testes:', error.message);
    process.exit(1);
  }
}

// Executar testes se este arquivo for executado diretamente
if (require.main === module) {
  runAllTests();
}

// Exportar funções de teste para uso com Jest
export {
  testValidations,
  testKeyTypes,
  testFormatting,
  testPayloadStructure,
  testDynamicPix,
  testConvenienceFunctions,
  runAllTests
};
