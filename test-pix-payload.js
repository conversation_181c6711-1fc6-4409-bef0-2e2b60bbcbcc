/**
 * Teste simples do PIX Payload Generator
 */

const { 
  generatePixPayload, 
  createPixPayload
} = require('./pix-payload-generator.js');

console.log('🚀 Testando PIX Payload Generator\n');

// Teste 1: PIX com chave CPF
console.log('1. Testando PIX com chave CPF:');
try {
  const pixCPF = createPixPayload(
    'CPF',
    '12345678909',
    'João <PERSON>',
    'São Paulo',
    100.50,
    'PAG001',
    'Pagamento de serviços'
  );
  console.log('✅ Sucesso!');
  console.log('Payload:', pixCPF);
  console.log('Tamanho:', pixCPF.length, 'caracteres');
  console.log('Inicia corretamente:', pixCPF.startsWith('00020101') ? '✅' : '❌');
  console.log('Termina com CRC:', /[0-9A-F]{4}$/.test(pixCPF) ? '✅' : '❌');
} catch (error) {
  console.error('❌ Erro:', error.message);
}

console.log('\n' + '='.repeat(60) + '\n');

// Teste 2: PIX com chave de telefone
console.log('2. Testando PIX com chave de telefone:');
try {
  const pixPhone = generatePixPayload({
    keyType: 'PHONE',
    key: '11999887766',
    nameReceiver: 'Maria Santos',
    cityReceiver: 'Rio de Janeiro',
    amount: 25.00,
    description: 'Transferência PIX'
  });
  console.log('✅ Sucesso!');
  console.log('Payload:', pixPhone);
  console.log('Contém telefone formatado:', pixPhone.includes('+5511999887766') ? '✅' : '❌');
} catch (error) {
  console.error('❌ Erro:', error.message);
}

console.log('\n' + '='.repeat(60) + '\n');

// Teste 3: PIX com chave email
console.log('3. Testando PIX com chave email:');
try {
  const pixEmail = generatePixPayload({
    keyType: 'EMAIL',
    key: '<EMAIL>',
    nameReceiver: 'Pedro Costa',
    cityReceiver: 'Belo Horizonte',
    amount: 0, // Valor aberto
    identification: 'DOA001',
    description: 'Doação'
  });
  console.log('✅ Sucesso!');
  console.log('Payload:', pixEmail);
  console.log('Contém email:', pixEmail.includes('<EMAIL>') ? '✅' : '❌');
} catch (error) {
  console.error('❌ Erro:', error.message);
}

console.log('\n' + '='.repeat(60) + '\n');

// Teste 4: PIX com chave aleatória
console.log('4. Testando PIX com chave aleatória:');
try {
  const randomKey = 'b5fe1edc-d108-410f-b966-eccaaca75e4f';
  const pixRandom = generatePixPayload({
    keyType: 'RANDOM',
    key: randomKey,
    nameReceiver: 'Ana Oliveira',
    cityReceiver: 'Brasília',
    amount: 150.75,
    zipcodeReceiver: '70040010',
    identification: 'VEN001',
    description: 'Venda de produto'
  });
  console.log('✅ Sucesso!');
  console.log('Payload:', pixRandom);
  console.log('Contém chave aleatória:', pixRandom.includes(randomKey) ? '✅' : '❌');
} catch (error) {
  console.error('❌ Erro:', error.message);
}

console.log('\n' + '='.repeat(60) + '\n');

// Teste 5: PIX dinâmico
console.log('5. Testando PIX dinâmico:');
try {
  const pixDynamic = generatePixPayload({
    keyType: 'RANDOM', // Será ignorado
    key: '', // Será ignorado
    dynamicUrl: 'psp.exemplo.com/pagamento/123456',
    nameReceiver: 'Loja Virtual',
    cityReceiver: 'São Paulo',
    singleTransaction: true,
    identification: 'ORD001',
    description: 'Compra online'
  });
  console.log('✅ Sucesso!');
  console.log('Payload:', pixDynamic);
  console.log('Contém URL dinâmica:', pixDynamic.includes('psp.exemplo.com/pagamento/123456') ? '✅' : '❌');
  console.log('É transação única:', pixDynamic.includes('010212') ? '✅' : '❌');
} catch (error) {
  console.error('❌ Erro:', error.message);
}

console.log('\n' + '='.repeat(60) + '\n');

// Teste 6: Validação de erros
console.log('6. Testando validação de erros:');

// Nome muito longo
try {
  generatePixPayload({
    keyType: 'RANDOM',
    key: 'test-key',
    nameReceiver: 'Nome extremamente longo que excede o limite de 25 caracteres permitidos',
    cityReceiver: 'São Paulo'
  });
  console.log('❌ Deveria ter dado erro para nome longo');
} catch (error) {
  console.log('✅ Erro capturado corretamente para nome longo:', error.message);
}

// Cidade muito longa
try {
  generatePixPayload({
    keyType: 'RANDOM',
    key: 'test-key',
    nameReceiver: 'Test',
    cityReceiver: 'Cidade com nome muito longo que excede limite'
  });
  console.log('❌ Deveria ter dado erro para cidade longa');
} catch (error) {
  console.log('✅ Erro capturado corretamente para cidade longa:', error.message);
}

// Sem chave nem URL
try {
  generatePixPayload({
    keyType: 'RANDOM',
    key: '',
    nameReceiver: 'Test',
    cityReceiver: 'City'
  });
  console.log('❌ Deveria ter dado erro para falta de chave');
} catch (error) {
  console.log('✅ Erro capturado corretamente para falta de chave:', error.message);
}

console.log('\n' + '='.repeat(60) + '\n');

// Teste 7: Formatação de texto
console.log('7. Testando formatação de texto:');
try {
  const pixFormatted = generatePixPayload({
    keyType: 'RANDOM',
    key: 'test-key',
    nameReceiver: 'José da Conceição',
    cityReceiver: 'São José',
    description: 'Pagamento à vista'
  });
  console.log('✅ Sucesso!');
  console.log('Acentos removidos do nome:', pixFormatted.includes('Jose da Conceicao') ? '✅' : '❌');
  console.log('Acentos removidos da cidade:', pixFormatted.includes('Sao Jose') ? '✅' : '❌');
} catch (error) {
  console.error('❌ Erro:', error.message);
}

console.log('\n🎉 Testes concluídos!');
