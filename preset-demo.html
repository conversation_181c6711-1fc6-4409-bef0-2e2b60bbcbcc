<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demonstração de Presets - QR Code Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            text-align: center;
            color: #718096;
            margin-bottom: 40px;
            font-size: 14px;
        }

        .preset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            margin-bottom: 40px;
        }

        .preset-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .preset-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }

        .preset-card.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }

        .preset-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .preset-name {
            font-weight: 600;
            font-size: 14px;
        }

        .qr-display-area {
            background: #f7fafc;
            border-radius: 16px;
            padding: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
        }

        #qr-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        }

        .specs-table {
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .specs-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .spec-row {
            display: grid;
            grid-template-columns: 180px 1fr;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .spec-row:last-child {
            border-bottom: none;
        }

        .spec-label {
            font-weight: 500;
            color: #4a5568;
        }

        .spec-value {
            color: #2d3748;
            font-family: 'Courier New', monospace;
        }

        .color-preview {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 3px;
            margin-right: 8px;
            vertical-align: middle;
            border: 1px solid #e2e8f0;
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #f8f9ff;
        }

        .input-group {
            margin-bottom: 30px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
            font-size: 14px;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Demonstração de Presets QR Code</h1>
        <p class="subtitle">Teste interativo dos 5 presets disponíveis</p>

        <div class="input-group">
            <label class="input-label" for="data-input">Dados para codificar:</label>
            <input 
                type="text" 
                id="data-input" 
                class="input-field" 
                value="https://example.com" 
                placeholder="Digite uma URL ou texto"
            >
        </div>

        <div class="preset-grid">
            <div class="preset-card active" data-preset="modern">
                <div class="preset-icon">🎨</div>
                <div class="preset-name">Moderno</div>
            </div>
            <div class="preset-card" data-preset="classic">
                <div class="preset-icon">📱</div>
                <div class="preset-name">Clássico</div>
            </div>
            <div class="preset-card" data-preset="elegant">
                <div class="preset-icon">✨</div>
                <div class="preset-name">Elegante</div>
            </div>
            <div class="preset-card" data-preset="vibrant">
                <div class="preset-icon">🌈</div>
                <div class="preset-name">Vibrante</div>
            </div>
            <div class="preset-card" data-preset="circular">
                <div class="preset-icon">⭕</div>
                <div class="preset-name">Circular</div>
            </div>
        </div>

        <div class="qr-display-area">
            <div id="qr-container"></div>
        </div>

        <div class="specs-table">
            <div class="specs-title">📊 Especificações Técnicas do Preset</div>
            <div class="spec-row">
                <div class="spec-label">Tipo dos Pontos:</div>
                <div class="spec-value" id="spec-dots-type">rounded</div>
            </div>
            <div class="spec-row">
                <div class="spec-label">Cor dos Pontos:</div>
                <div class="spec-value">
                    <span class="color-preview" id="spec-dots-color-preview" style="background: #667eea;"></span>
                    <span id="spec-dots-color">#667eea</span>
                </div>
            </div>
            <div class="spec-row">
                <div class="spec-label">Tipo Quadrado Canto:</div>
                <div class="spec-value" id="spec-corner-square-type">extra-rounded</div>
            </div>
            <div class="spec-row">
                <div class="spec-label">Cor Quadrado Canto:</div>
                <div class="spec-value">
                    <span class="color-preview" id="spec-corner-square-color-preview" style="background: #764ba2;"></span>
                    <span id="spec-corner-square-color">#764ba2</span>
                </div>
            </div>
            <div class="spec-row">
                <div class="spec-label">Tipo Ponto Central:</div>
                <div class="spec-value" id="spec-corner-dot-type">rounded</div>
            </div>
            <div class="spec-row">
                <div class="spec-label">Cor Ponto Central:</div>
                <div class="spec-value">
                    <span class="color-preview" id="spec-corner-dot-color-preview" style="background: #667eea;"></span>
                    <span id="spec-corner-dot-color">#667eea</span>
                </div>
            </div>
            <div class="spec-row">
                <div class="spec-label">Cor de Fundo:</div>
                <div class="spec-value">
                    <span class="color-preview" id="spec-background-color-preview" style="background: #ffffff;"></span>
                    <span id="spec-background-color">#ffffff</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="downloadQR('png')">
                📥 Download PNG
            </button>
            <button class="btn btn-secondary" onclick="downloadQR('svg')">
                📥 Download SVG
            </button>
            <button class="btn btn-secondary" onclick="copySpecs()">
                📋 Copiar Especificações
            </button>
        </div>
    </div>

    <script src="lib/qr-code-styling.js"></script>
    <script>
        // Definição completa dos presets
        const PRESETS = {
            modern: {
                dotsType: 'rounded',
                dotsColor: '#667eea',
                cornerSquareType: 'extra-rounded',
                cornerSquareColor: '#764ba2',
                cornerDotType: 'rounded',
                cornerDotColor: '#667eea',
                backgroundColor: '#ffffff',
            },
            classic: {
                dotsType: 'square',
                dotsColor: '#000000',
                cornerSquareType: 'square',
                cornerSquareColor: '#000000',
                cornerDotType: 'square',
                cornerDotColor: '#000000',
                backgroundColor: '#ffffff',
            },
            elegant: {
                dotsType: 'classy-rounded',
                dotsColor: '#2c3e50',
                cornerSquareType: 'rounded',
                cornerSquareColor: '#34495e',
                cornerDotType: 'rounded',
                cornerDotColor: '#2c3e50',
                backgroundColor: '#ecf0f1',
            },
            vibrant: {
                dotsType: 'dots',
                dotsColor: '#e74c3c',
                cornerSquareType: 'extra-rounded',
                cornerSquareColor: '#3498db',
                cornerDotType: 'dot',
                cornerDotColor: '#f39c12',
                backgroundColor: '#ffffff',
            },
            circular: {
                dotsType: 'rounded',
                dotsColor: '#2c3e50',
                cornerSquareType: 'dot',
                cornerSquareColor: '#e74c3c',
                cornerDotType: 'dot',
                cornerDotColor: '#3498db',
                backgroundColor: '#ffffff',
            },
        };

        let currentPreset = 'modern';
        let qrCode = null;

        // Inicializar
        document.addEventListener('DOMContentLoaded', () => {
            // Event listeners para os cards de preset
            document.querySelectorAll('.preset-card').forEach(card => {
                card.addEventListener('click', () => {
                    selectPreset(card.dataset.preset);
                });
            });

            // Event listener para o input
            document.getElementById('data-input').addEventListener('input', (e) => {
                updateQRCode();
            });

            // Gerar QR inicial
            generateQRCode();
        });

        function selectPreset(presetName) {
            // Atualizar visual dos cards
            document.querySelectorAll('.preset-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-preset="${presetName}"]`).classList.add('active');

            currentPreset = presetName;
            updateSpecs();
            updateQRCode();
        }

        function generateQRCode() {
            const preset = PRESETS[currentPreset];
            const data = document.getElementById('data-input').value || 'https://example.com';

            const options = {
                width: 300,
                height: 300,
                type: 'svg',
                data: data,
                margin: 10,
                qrOptions: {
                    typeNumber: 0,
                    mode: undefined,
                    errorCorrectionLevel: 'M',
                },
                dotsOptions: {
                    color: preset.dotsColor,
                    type: preset.dotsType,
                    roundSize: true,
                },
                backgroundOptions: {
                    color: preset.backgroundColor,
                    round: 0,
                },
                cornersSquareOptions: {
                    color: preset.cornerSquareColor,
                    type: preset.cornerSquareType,
                },
                cornersDotOptions: {
                    color: preset.cornerDotColor,
                    type: preset.cornerDotType,
                },
            };

            // Limpar container
            document.getElementById('qr-container').innerHTML = '';

            // Criar novo QR
            qrCode = new QRCodeStyling(options);
            qrCode.append(document.getElementById('qr-container'));
        }

        function updateQRCode() {
            const preset = PRESETS[currentPreset];
            const data = document.getElementById('data-input').value || 'https://example.com';

            if (qrCode) {
                qrCode.update({
                    data: data,
                    dotsOptions: {
                        color: preset.dotsColor,
                        type: preset.dotsType,
                    },
                    cornersSquareOptions: {
                        color: preset.cornerSquareColor,
                        type: preset.cornerSquareType,
                    },
                    cornersDotOptions: {
                        color: preset.cornerDotColor,
                        type: preset.cornerDotType,
                    },
                    backgroundOptions: {
                        color: preset.backgroundColor,
                    },
                });
            } else {
                generateQRCode();
            }
        }

        function updateSpecs() {
            const preset = PRESETS[currentPreset];

            // Atualizar textos
            document.getElementById('spec-dots-type').textContent = preset.dotsType;
            document.getElementById('spec-dots-color').textContent = preset.dotsColor;
            document.getElementById('spec-corner-square-type').textContent = preset.cornerSquareType;
            document.getElementById('spec-corner-square-color').textContent = preset.cornerSquareColor;
            document.getElementById('spec-corner-dot-type').textContent = preset.cornerDotType;
            document.getElementById('spec-corner-dot-color').textContent = preset.cornerDotColor;
            document.getElementById('spec-background-color').textContent = preset.backgroundColor;

            // Atualizar previews de cor
            document.getElementById('spec-dots-color-preview').style.background = preset.dotsColor;
            document.getElementById('spec-corner-square-color-preview').style.background = preset.cornerSquareColor;
            document.getElementById('spec-corner-dot-color-preview').style.background = preset.cornerDotColor;
            document.getElementById('spec-background-color-preview').style.background = preset.backgroundColor;
        }

        function downloadQR(format) {
            if (qrCode) {
                qrCode.download({
                    name: `qrcode-${currentPreset}`,
                    extension: format,
                });
            }
        }

        function copySpecs() {
            const preset = PRESETS[currentPreset];
            const specs = `Preset: ${currentPreset}
dotsType: '${preset.dotsType}'
dotsColor: '${preset.dotsColor}'
cornerSquareType: '${preset.cornerSquareType}'
cornerSquareColor: '${preset.cornerSquareColor}'
cornerDotType: '${preset.cornerDotType}'
cornerDotColor: '${preset.cornerDotColor}'
backgroundColor: '${preset.backgroundColor}'`;

            navigator.clipboard.writeText(specs).then(() => {
                alert('Especificações copiadas para a área de transferência!');
            });
        }
    </script>
</body>
</html>
