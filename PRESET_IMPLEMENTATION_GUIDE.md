# Guia de Implementação dos Presets de QR Code

## Introdução

Este guia fornece instruções detalhadas para implementar os presets de QR Code em uma aplicação refatorada, garantindo reprodução pixel-perfect dos designs originais.

## Estrutura de Dados dos Presets

### Objeto Completo de Presets
```javascript
const QR_CODE_PRESETS = {
  modern: {
    dotsType: 'rounded',
    dotsColor: '#667eea',
    cornerSquareType: 'extra-rounded',
    cornerSquareColor: '#764ba2',
    cornerDotType: 'rounded',
    cornerDotColor: '#667eea',
    backgroundColor: '#ffffff',
  },
  classic: {
    dotsType: 'square',
    dotsColor: '#000000',
    cornerSquareType: 'square',
    cornerSquareColor: '#000000',
    cornerDotType: 'square',
    cornerDotColor: '#000000',
    backgroundColor: '#ffffff',
  },
  elegant: {
    dotsType: 'classy-rounded',
    dotsColor: '#2c3e50',
    cornerSquareType: 'rounded',
    cornerSquareColor: '#34495e',
    cornerDotType: 'rounded',
    cornerDotColor: '#2c3e50',
    backgroundColor: '#ecf0f1',
  },
  vibrant: {
    dotsType: 'dots',
    dotsColor: '#e74c3c',
    cornerSquareType: 'extra-rounded',
    cornerSquareColor: '#3498db',
    cornerDotType: 'dot',
    cornerDotColor: '#f39c12',
    backgroundColor: '#ffffff',
  },
  circular: {
    dotsType: 'rounded',
    dotsColor: '#2c3e50',
    cornerSquareType: 'dot',
    cornerSquareColor: '#e74c3c',
    cornerDotType: 'dot',
    cornerDotColor: '#3498db',
    backgroundColor: '#ffffff',
  },
};
```

## Classe de Gerenciamento de Presets

### Implementação Completa
```javascript
class QRCodePresetManager {
  constructor() {
    this.currentPreset = null;
    this.customSettings = {};
    this.qrCodeInstance = null;
  }

  /**
   * Aplica um preset específico
   * @param {string} presetName - Nome do preset (modern, classic, elegant, vibrant, circular)
   * @returns {Object} Configurações do preset aplicado
   */
  applyPreset(presetName) {
    if (!QR_CODE_PRESETS[presetName]) {
      throw new Error(`Preset "${presetName}" não encontrado`);
    }

    const preset = QR_CODE_PRESETS[presetName];
    this.currentPreset = presetName;

    // Atualizar interface se elementos existirem
    this.updateUIElements(preset);

    // Retornar configurações para uso direto
    return this.buildQRCodeOptions(preset);
  }

  /**
   * Atualiza elementos da interface com valores do preset
   * @param {Object} preset - Objeto com configurações do preset
   */
  updateUIElements(preset) {
    const elements = {
      'dots-type': preset.dotsType,
      'dots-color': preset.dotsColor,
      'corner-square-type': preset.cornerSquareType,
      'corner-square-color': preset.cornerSquareColor,
      'corner-dot-type': preset.cornerDotType,
      'corner-dot-color': preset.cornerDotColor,
      'background-color': preset.backgroundColor,
    };

    for (const [elementId, value] of Object.entries(elements)) {
      const element = document.getElementById(elementId);
      if (element) {
        element.value = value;
        // Disparar evento de mudança para atualizar preview
        element.dispatchEvent(new Event('change', { bubbles: true }));
      }
    }
  }

  /**
   * Constrói opções completas para QRCodeStyling
   * @param {Object} preset - Configurações do preset
   * @param {Object} additionalOptions - Opções adicionais (data, size, etc)
   * @returns {Object} Opções formatadas para QRCodeStyling
   */
  buildQRCodeOptions(preset, additionalOptions = {}) {
    const defaultOptions = {
      width: 300,
      height: 300,
      type: 'svg',
      data: '',
      margin: 10,
      qrOptions: {
        typeNumber: 0,
        mode: undefined,
        errorCorrectionLevel: 'M',
      },
      dotsOptions: {
        color: preset.dotsColor,
        type: preset.dotsType,
        roundSize: true,
      },
      backgroundOptions: {
        color: preset.backgroundColor,
        round: 0,
      },
      cornersSquareOptions: {
        color: preset.cornerSquareColor,
        type: preset.cornerSquareType,
      },
      cornersDotOptions: {
        color: preset.cornerDotColor,
        type: preset.cornerDotType,
      },
    };

    // Mesclar com opções adicionais
    return this.deepMerge(defaultOptions, additionalOptions);
  }

  /**
   * Gera QR Code com preset aplicado
   * @param {string} data - Dados para codificar
   * @param {string} presetName - Nome do preset
   * @param {HTMLElement} container - Elemento onde renderizar
   */
  generateQRCode(data, presetName, container) {
    const options = this.applyPreset(presetName);
    options.data = data;

    // Limpar instância anterior se existir
    if (this.qrCodeInstance) {
      container.innerHTML = '';
    }

    // Criar nova instância
    this.qrCodeInstance = new QRCodeStyling(options);
    this.qrCodeInstance.append(container);

    return this.qrCodeInstance;
  }

  /**
   * Exporta QR Code atual
   * @param {string} format - Formato de exportação (png, svg, jpeg, webp)
   * @param {string} filename - Nome do arquivo
   */
  async exportQRCode(format = 'png', filename = 'qrcode') {
    if (!this.qrCodeInstance) {
      throw new Error('Nenhum QR Code gerado');
    }

    const extension = format.toLowerCase();
    await this.qrCodeInstance.download({
      name: filename,
      extension: extension,
    });
  }

  /**
   * Utility: Deep merge de objetos
   */
  deepMerge(target, source) {
    const output = Object.assign({}, target);
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach(key => {
        if (this.isObject(source[key])) {
          if (!(key in target))
            Object.assign(output, { [key]: source[key] });
          else
            output[key] = this.deepMerge(target[key], source[key]);
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    return output;
  }

  isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
  }
}
```

## Implementação React

### Componente de Presets
```jsx
import React, { useState, useRef, useEffect } from 'react';
import QRCodeStyling from 'qr-code-styling';

const QRCodePresets = ({ data = 'https://example.com' }) => {
  const [selectedPreset, setSelectedPreset] = useState('modern');
  const [qrCode, setQrCode] = useState(null);
  const qrRef = useRef(null);

  const presets = {
    modern: {
      label: '🎨 Moderno',
      config: {
        dotsType: 'rounded',
        dotsColor: '#667eea',
        cornerSquareType: 'extra-rounded',
        cornerSquareColor: '#764ba2',
        cornerDotType: 'rounded',
        cornerDotColor: '#667eea',
        backgroundColor: '#ffffff',
      }
    },
    classic: {
      label: '📱 Clássico',
      config: {
        dotsType: 'square',
        dotsColor: '#000000',
        cornerSquareType: 'square',
        cornerSquareColor: '#000000',
        cornerDotType: 'square',
        cornerDotColor: '#000000',
        backgroundColor: '#ffffff',
      }
    },
    elegant: {
      label: '✨ Elegante',
      config: {
        dotsType: 'classy-rounded',
        dotsColor: '#2c3e50',
        cornerSquareType: 'rounded',
        cornerSquareColor: '#34495e',
        cornerDotType: 'rounded',
        cornerDotColor: '#2c3e50',
        backgroundColor: '#ecf0f1',
      }
    },
    vibrant: {
      label: '🌈 Vibrante',
      config: {
        dotsType: 'dots',
        dotsColor: '#e74c3c',
        cornerSquareType: 'extra-rounded',
        cornerSquareColor: '#3498db',
        cornerDotType: 'dot',
        cornerDotColor: '#f39c12',
        backgroundColor: '#ffffff',
      }
    },
    circular: {
      label: '⭕ Circular',
      config: {
        dotsType: 'rounded',
        dotsColor: '#2c3e50',
        cornerSquareType: 'dot',
        cornerSquareColor: '#e74c3c',
        cornerDotType: 'dot',
        cornerDotColor: '#3498db',
        backgroundColor: '#ffffff',
      }
    }
  };

  useEffect(() => {
    if (!qrCode) {
      const preset = presets[selectedPreset].config;
      const newQRCode = new QRCodeStyling({
        width: 300,
        height: 300,
        type: 'svg',
        data: data,
        dotsOptions: {
          color: preset.dotsColor,
          type: preset.dotsType,
          roundSize: true,
        },
        cornersSquareOptions: {
          color: preset.cornerSquareColor,
          type: preset.cornerSquareType,
        },
        cornersDotOptions: {
          color: preset.cornerDotColor,
          type: preset.cornerDotType,
        },
        backgroundOptions: {
          color: preset.backgroundColor,
        },
        qrOptions: {
          errorCorrectionLevel: 'M',
        }
      });
      setQrCode(newQRCode);
    }
  }, []);

  useEffect(() => {
    if (qrCode && qrRef.current) {
      qrRef.current.innerHTML = '';
      qrCode.append(qrRef.current);
    }
  }, [qrCode]);

  useEffect(() => {
    if (qrCode) {
      const preset = presets[selectedPreset].config;
      qrCode.update({
        dotsOptions: {
          color: preset.dotsColor,
          type: preset.dotsType,
        },
        cornersSquareOptions: {
          color: preset.cornerSquareColor,
          type: preset.cornerSquareType,
        },
        cornersDotOptions: {
          color: preset.cornerDotColor,
          type: preset.cornerDotType,
        },
        backgroundOptions: {
          color: preset.backgroundColor,
        },
      });
    }
  }, [selectedPreset, qrCode]);

  const handleDownload = (format) => {
    if (qrCode) {
      qrCode.download({
        name: `qrcode-${selectedPreset}`,
        extension: format,
      });
    }
  };

  return (
    <div className="qr-preset-container">
      <div className="preset-buttons">
        {Object.entries(presets).map(([key, preset]) => (
          <button
            key={key}
            onClick={() => setSelectedPreset(key)}
            className={`preset-btn ${selectedPreset === key ? 'active' : ''}`}
          >
            {preset.label}
          </button>
        ))}
      </div>
      
      <div className="qr-display" ref={qrRef}></div>
      
      <div className="export-buttons">
        <button onClick={() => handleDownload('png')}>Download PNG</button>
        <button onClick={() => handleDownload('svg')}>Download SVG</button>
      </div>
    </div>
  );
};

export default QRCodePresets;
```

## Implementação Vue.js

### Componente Vue
```vue
<template>
  <div class="qr-preset-manager">
    <div class="preset-selector">
      <button
        v-for="(preset, key) in presetList"
        :key="key"
        @click="selectPreset(key)"
        :class="['preset-btn', { active: currentPreset === key }]"
      >
        {{ preset.label }}
      </button>
    </div>

    <div class="qr-container" ref="qrContainer"></div>

    <div class="actions">
      <button @click="downloadQR('png')">Download PNG</button>
      <button @click="downloadQR('svg')">Download SVG</button>
    </div>
  </div>
</template>

<script>
import QRCodeStyling from 'qr-code-styling';

export default {
  name: 'QRCodePresets',
  props: {
    data: {
      type: String,
      default: 'https://example.com'
    }
  },
  data() {
    return {
      currentPreset: 'modern',
      qrCode: null,
      presetList: {
        modern: {
          label: '🎨 Moderno',
          dotsType: 'rounded',
          dotsColor: '#667eea',
          cornerSquareType: 'extra-rounded',
          cornerSquareColor: '#764ba2',
          cornerDotType: 'rounded',
          cornerDotColor: '#667eea',
          backgroundColor: '#ffffff',
        },
        classic: {
          label: '📱 Clássico',
          dotsType: 'square',
          dotsColor: '#000000',
          cornerSquareType: 'square',
          cornerSquareColor: '#000000',
          cornerDotType: 'square',
          cornerDotColor: '#000000',
          backgroundColor: '#ffffff',
        },
        elegant: {
          label: '✨ Elegante',
          dotsType: 'classy-rounded',
          dotsColor: '#2c3e50',
          cornerSquareType: 'rounded',
          cornerSquareColor: '#34495e',
          cornerDotType: 'rounded',
          cornerDotColor: '#2c3e50',
          backgroundColor: '#ecf0f1',
        },
        vibrant: {
          label: '🌈 Vibrante',
          dotsType: 'dots',
          dotsColor: '#e74c3c',
          cornerSquareType: 'extra-rounded',
          cornerSquareColor: '#3498db',
          cornerDotType: 'dot',
          cornerDotColor: '#f39c12',
          backgroundColor: '#ffffff',
        },
        circular: {
          label: '⭕ Circular',
          dotsType: 'rounded',
          dotsColor: '#2c3e50',
          cornerSquareType: 'dot',
          cornerSquareColor: '#e74c3c',
          cornerDotType: 'dot',
          cornerDotColor: '#3498db',
          backgroundColor: '#ffffff',
        }
      }
    };
  },
  mounted() {
    this.initQRCode();
  },
  methods: {
    initQRCode() {
      const preset = this.presetList[this.currentPreset];
      
      this.qrCode = new QRCodeStyling({
        width: 300,
        height: 300,
        type: 'svg',
        data: this.data,
        dotsOptions: {
          color: preset.dotsColor,
          type: preset.dotsType,
          roundSize: true,
        },
        cornersSquareOptions: {
          color: preset.cornerSquareColor,
          type: preset.cornerSquareType,
        },
        cornersDotOptions: {
          color: preset.cornerDotColor,
          type: preset.cornerDotType,
        },
        backgroundOptions: {
          color: preset.backgroundColor,
        },
        qrOptions: {
          errorCorrectionLevel: 'M',
        }
      });

      this.qrCode.append(this.$refs.qrContainer);
    },
    selectPreset(presetKey) {
      this.currentPreset = presetKey;
      this.updateQRCode();
    },
    updateQRCode() {
      const preset = this.presetList[this.currentPreset];
      
      this.qrCode.update({
        dotsOptions: {
          color: preset.dotsColor,
          type: preset.dotsType,
        },
        cornersSquareOptions: {
          color: preset.cornerSquareColor,
          type: preset.cornerSquareType,
        },
        cornersDotOptions: {
          color: preset.cornerDotColor,
          type: preset.cornerDotType,
        },
        backgroundOptions: {
          color: preset.backgroundColor,
        },
      });
    },
    downloadQR(format) {
      this.qrCode.download({
        name: `qrcode-${this.currentPreset}`,
        extension: format,
      });
    }
  }
};
</script>
```

## Validação e Testes

### Script de Validação dos Presets
```javascript
// test-presets.js
import QRCodeStyling from 'qr-code-styling';

const testData = 'https://example.com/test';

function validatePreset(presetName, presetConfig) {
  console.log(`\n🔍 Testando preset: ${presetName}`);
  
  try {
    // Validar tipos de dots
    const validDotTypes = ['square', 'rounded', 'dots', 'classy', 'classy-rounded', 'extra-rounded'];
    if (!validDotTypes.includes(presetConfig.dotsType)) {
      throw new Error(`Tipo de dot inválido: ${presetConfig.dotsType}`);
    }
    
    // Validar cores (formato hex)
    const hexColorRegex = /^#[0-9A-Fa-f]{6}$/;
    const colors = [
      presetConfig.dotsColor,
      presetConfig.cornerSquareColor,
      presetConfig.cornerDotColor,
      presetConfig.backgroundColor
    ];
    
    colors.forEach(color => {
      if (!hexColorRegex.test(color)) {
        throw new Error(`Cor inválida: ${color}`);
      }
    });
    
    // Criar QR Code para validar renderização
    const qr = new QRCodeStyling({
      width: 300,
      height: 300,
      type: 'svg',
      data: testData,
      dotsOptions: {
        color: presetConfig.dotsColor,
        type: presetConfig.dotsType,
      },
      cornersSquareOptions: {
        color: presetConfig.cornerSquareColor,
        type: presetConfig.cornerSquareType,
      },
      cornersDotOptions: {
        color: presetConfig.cornerDotColor,
        type: presetConfig.cornerDotType,
      },
      backgroundOptions: {
        color: presetConfig.backgroundColor,
      },
    });
    
    console.log(`✅ Preset ${presetName} validado com sucesso`);
    return true;
    
  } catch (error) {
    console.error(`❌ Erro no preset ${presetName}: ${error.message}`);
    return false;
  }
}

// Executar testes
const presets = {
  modern: {
    dotsType: 'rounded',
    dotsColor: '#667eea',
    cornerSquareType: 'extra-rounded',
    cornerSquareColor: '#764ba2',
    cornerDotType: 'rounded',
    cornerDotColor: '#667eea',
    backgroundColor: '#ffffff',
  },
  classic: {
    dotsType: 'square',
    dotsColor: '#000000',
    cornerSquareType: 'square',
    cornerSquareColor: '#000000',
    cornerDotType: 'square',
    cornerDotColor: '#000000',
    backgroundColor: '#ffffff',
  },
  elegant: {
    dotsType: 'classy-rounded',
    dotsColor: '#2c3e50',
    cornerSquareType: 'rounded',
    cornerSquareColor: '#34495e',
    cornerDotType: 'rounded',
    cornerDotColor: '#2c3e50',
    backgroundColor: '#ecf0f1',
  },
  vibrant: {
    dotsType: 'dots',
    dotsColor: '#e74c3c',
    cornerSquareType: 'extra-rounded',
    cornerSquareColor: '#3498db',
    cornerDotType: 'dot',
    cornerDotColor: '#f39c12',
    backgroundColor: '#ffffff',
  },
  circular: {
    dotsType: 'rounded',
    dotsColor: '#2c3e50',
    cornerSquareType: 'dot',
    cornerSquareColor: '#e74c3c',
    cornerDotType: 'dot',
    cornerDotColor: '#3498db',
    backgroundColor: '#ffffff',
  },
};

Object.entries(presets).forEach(([name, config]) => {
  validatePreset(name, config);
});
```

## Estilos CSS Necessários

```css
/* Botões de Preset */
.preset-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.preset-btn {
  padding: 10px 15px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.preset-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: #f8f9ff;
}

.preset-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* Container do QR Code */
.qr-container,
.qr-display {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-height: 350px;
}

/* Botões de Export */
.export-buttons,
.actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  justify-content: center;
}

.export-buttons button,
.actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background: #667eea;
  color: white;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.export-buttons button:hover,
.actions button:hover {
  background: #5a67d8;
}
```

## Troubleshooting

### Problema: Preset não renderiza corretamente
**Solução:** Verificar se a versão da biblioteca qr-code-styling é exatamente 1.9.2

### Problema: Cores aparecem diferentes
**Solução:** Garantir que as cores estão em formato hexadecimal de 6 dígitos (#RRGGBB)

### Problema: Tipos de forma não reconhecidos
**Solução:** Verificar ortografia exata dos tipos (case-sensitive, usar hífen não underscore)

### Problema: QR Code não lê após aplicar preset
**Solução:** Garantir que errorCorrectionLevel está configurado como 'M' ou superior

## Conclusão

Este guia fornece todas as ferramentas necessárias para implementar os presets de QR Code em qualquer framework JavaScript moderno, mantendo fidelidade visual completa aos designs originais.
