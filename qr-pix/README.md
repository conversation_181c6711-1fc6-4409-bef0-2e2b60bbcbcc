# pypix-ts

**pypix-ts** is a TypeScript/JavaScript port of the [PyPix](https://github.com/cleitonleonel/pypix) library, originally based on [GPIX](https://github.com/hiagodotme/gpix.git) by <PERSON><PERSON>.

This library facilitates the generation of static and dynamic BR-Code for PIX transactions, with advanced QR Code customization and visual styling capabilities.

---

## 🛠️ Installation

### System Requirements

- **Node.js**: 16.0+ 
- **Operating System**: macOS, Linux, Windows
- **Canvas Dependencies**: Required for QR Code styling (see Platform-specific setup below)

### Platform-specific Setup

**macOS:**
```bash
# Canvas dependencies are usually installed automatically
# If you encounter issues:
brew install pkg-config cairo pango libpng jpeg giflib librsvg
```

**Ubuntu/Debian:**
```bash
sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev
```

**Windows:**
```bash
# Use npm with --canvas_binary_host_mirror for better compatibility
npm install --canvas_binary_host_mirror=https://github.com/Automattic/node-canvas/releases/download/v2.6.0/
```

### Installation

```bash
npm install pypix-ts
# or
yarn add pypix-ts
```

---

## ▶️ How to Use

### Basic Example

```typescript
import { 
  Pix, 
  parseBrCode, 
  MarkerStyle, 
  BorderStyle, 
  LineStyle, 
  FrameStyle, 
  GradientMode 
} from 'pypix-ts';

// Create PIX instance
const pix = new Pix();

// Configure PIX data
pix.setNameReceiver('João Silva');
pix.setCityReceiver('São Paulo');
pix.setKey('+5511999887766'); // Phone, CPF, email, or random key
pix.setAmount(25.50);
pix.setDescription('Payment for services');
pix.setIdentification('INV001');

// Generate BR-Code
const brCode = pix.getBrCode();
console.log('PIX BR-Code:', brCode);

// Parse BR-Code back to data
const parsedData = parseBrCode(brCode);
console.log('Parsed data:', parsedData);

// Generate styled QR Code
const base64qr = await pix.saveQrcode({
  output: './qrcode.png',
  boxSize: 7,
  border: 1,
  customLogo: './logo.png', // Optional PNG or GIF
  markerStyle: MarkerStyle.QUARTER_CIRCLE,
  borderStyle: BorderStyle.ROUNDED,
  lineStyle: LineStyle.ROUNDED,
  gradientColor: 'purple',
  gradientMode: GradientMode.GRADIENT,
  frameStyle: FrameStyle.SCAN_ME_PURPLE,
  styleMode: 'Full'
});

if (base64qr) {
  console.log('QR Code generated successfully!');
}
```

### Constructor Configuration

```typescript
// Configure via constructor
const pix = new Pix({
  nameReceiver: 'João Silva',
  cityReceiver: 'São Paulo',
  key: '+5511999887766',
  amount: 25.50,
  description: 'Payment for services',
  identification: 'INV001'
});

const brCode = pix.getBrCode();
```

### Different PIX Types

```typescript
// 1. Static PIX with fixed amount
const staticPix = new Pix();
staticPix.setNameReceiver('Merchant');
staticPix.setCityReceiver('City');
staticPix.setKey('b5fe1edc-d108-410f-b966-eccaaca75e4f');
staticPix.setAmount(10.00); // Fixed amount
const staticCode = staticPix.getBrCode();

// 2. Static PIX with open amount  
const openPix = new Pix();
openPix.setNameReceiver('Merchant');
openPix.setCityReceiver('City');
openPix.setKey('b5fe1edc-d108-410f-b966-eccaaca75e4f');
openPix.setAmount(0); // Open amount - user chooses
const openCode = openPix.getBrCode();

// 3. Dynamic PIX (requires PSP URL)
const dynamicPix = new Pix();
dynamicPix.setNameReceiver('Merchant');
dynamicPix.setCityReceiver('City');
dynamicPix.setDefaultUrlPix('psp.example.com/payment/123');
dynamicPix.isSingleTransaction(true);
const dynamicCode = dynamicPix.getBrCode();
```

---

## 🎨 Styling Options

### Marker Styles
```typescript
enum MarkerStyle {
  SQUARE = 'square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  QUARTER_CIRCLE = 'quarter_circle',
  STAR = 'star',
  DIAMOND = 'diamond',
  PLUS = 'plus'
}
```

### Border Styles  
```typescript
enum BorderStyle {
  SQUARE = 'square',
  ROUNDED = 'rounded',
  CIRCLE = 'circle'
}
```

### Line Styles
```typescript
enum LineStyle {
  SQUARE = 'square',
  GAPPED_SQUARE = 'gapped_square', 
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  VERTICAL_BARS = 'vertical_bars',
  HORIZONTAL_BARS = 'horizontal_bars'
}
```

### Frame Styles
```typescript
enum FrameStyle {
  CLEAN = 'clean',
  TECH = 'tech',
  CREATIVE = 'creative', 
  PAY = 'pay',
  SCAN_ME_PURPLE = 'scan_me_purple',
  SCAN_ME_NEON = 'scan_me_neon',
  SCAN_ME_TECH = 'scan_me_tech'
}
```

### Gradient Modes
```typescript
enum GradientMode {
  NORMAL = 'normal',    // Solid color
  GRADIENT = 'gradient', // Linear gradient
  MULTI = 'multi'       // Radial multicolor gradient
}
```

---

## 🔧 API Reference

### Pix Class Methods

```typescript
// Configuration methods
setNameReceiver(name: string): void           // Max 25 characters
setCityReceiver(city: string): void           // Max 15 characters  
setKey(key: string): void                     // PIX key (phone/CPF/email/random)
setAmount(amount: number): void               // Transaction amount
setDescription(description: string): void     // Transaction description
setIdentification(id: string): void           // Transaction ID
setZipcodeReceiver(zipcode: string): void     // Postal code
setDefaultUrlPix(url: string): void           // For dynamic PIX
isSingleTransaction(single: boolean): void    // Single-use transaction

// Generation methods
getBrCode(): string                           // Generate BR-Code string
saveQrcode(options: QRCodeOptions): Promise<string> // Generate QR Code

// Constructor
constructor(config?: PixConfig)               // Optional initial configuration
```

### QR Code Options

```typescript
interface QRCodeOptions {
  data?: string;              // BR-Code data (auto-generated if not provided)
  output?: string;            // Output file path
  boxSize?: number;           // QR module size (default: 7)
  border?: number;            // Border size (default: 1)
  customLogo?: string;        // Logo file path (PNG/GIF)
  markerStyle?: MarkerStyle;  // Position marker style
  borderStyle?: BorderStyle;  // Marker border style
  lineStyle?: LineStyle;      // QR module style
  gradientColor?: string;     // Gradient base color
  gradientMode?: GradientMode; // Gradient type
  frameStyle?: FrameStyle;    // Decorative frame
  styleMode?: 'Normal' | 'Full'; // Styling level
}
```

### Utility Functions

```typescript
// PIX validation
validateCPF(cpf: string): boolean           // Validate Brazilian CPF
validatePhone(phone: string): boolean       // Validate phone number

// BR-Code parsing  
parseBrCode(brCode: string): ParsedBRCode   // Parse BR-Code to object
crcCompute(data: string): string           // Calculate CRC16 checksum

// Text formatting
formattedText(text: string): string        // Format for PIX standards
getValue(tag: string, value: string): string // TLV formatting
```

---

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file  
npm test -- pix.test.ts
```

---

## 📚 Development

```bash
# Install dependencies
npm install

# Build TypeScript
npm run build  

# Run development server
npm run dev

# Lint code
npm run lint

# Format code
npm run format
```

---

## 🔍 Troubleshooting  

### Canvas Installation Issues

**Error: "Cannot find module 'canvas'"**

```bash
# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# On macOS with M1/M2 chips:
arch -x86_64 npm install canvas
```

**Error: "node-pre-gyp: command not found"**

```bash
npm install -g node-pre-gyp
npm install canvas
```

### QR Code Generation Issues

1. Ensure all required fields are set (name, city, key)
2. Verify logo file exists if using custom logo
3. Check file permissions for output directory
4. Use absolute paths for file references

---

## 📝 Differences from Python Version

- **Canvas**: Uses Node.js Canvas instead of Cairo/Pillow
- **Sharp**: Image processing with Sharp instead of Pillow  
- **Async**: QR generation is asynchronous
- **Types**: Full TypeScript support with type definitions
- **Dependencies**: Different image processing libraries
- **Performance**: Similar performance, optimized for Node.js

---
