/**
 * PIX Payload Generator - Módulo independente para geração de payload PIX
 * 
 * Este módulo contém apenas a lógica necessária para gerar o payload PIX (BR-Code)
 * seguindo o padrão EMV QR Code do Banco Central do Brasil.
 * 
 * Não inclui funcionalidades de geração de QR Code ou renderização visual.
 */

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

export interface PixPayloadConfig {
  /** Tipo da chave PIX */
  keyType: 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE' | 'RANDOM';
  /** Valor da chave PIX */
  key: string;
  /** Nome do beneficiário (máximo 25 caracteres) */
  nameReceiver: string;
  /** Cidade do beneficiário (máximo 15 caracteres) */
  cityReceiver: string;
  /** Valor da transação (opcional, 0 para valor aberto) */
  amount?: number;
  /** CEP do beneficiário (opcional) */
  zipcodeReceiver?: string;
  /** Identificador da transação (opcional) */
  identification?: string;
  /** Descrição da transação (opcional) */
  description?: string;
  /** Se é transação única (para PIX dinâmico) */
  singleTransaction?: boolean;
  /** URL para PIX dinâmico (alternativa à chave) */
  dynamicUrl?: string;
}

export interface ParsedPixPayload {
  nome?: string;
  cidade?: string;
  valor?: number;
  chave?: string;
  txid?: string;
  cep?: string;
  descricao?: string;
  [key: string]: any;
}

// ============================================================================
// UTILITÁRIOS DE VALIDAÇÃO
// ============================================================================

/**
 * Valida CPF brasileiro
 */
function validateCPF(numbers: string): boolean {
  const cpf = numbers.replace(/\D/g, '').split('').map(Number);
  
  if (cpf.length !== 11) {
    return false;
  }
  
  // Verifica se todos os dígitos são iguais
  if (cpf.every(digit => digit === cpf[0])) {
    return false;
  }
  
  // Valida dígitos verificadores
  for (let i = 9; i < 11; i++) {
    const value = cpf.slice(0, i).reduce((sum, digit, index) => {
      return sum + digit * ((i + 1) - index);
    }, 0);
    const digit = ((value * 10) % 11) % 10;
    if (digit !== cpf[i]) {
      return false;
    }
  }
  
  return true;
}

/**
 * Valida CNPJ brasileiro
 */
function validateCNPJ(cnpj: string): boolean {
  const numbers = cnpj.replace(/\D/g, '');
  
  if (numbers.length !== 14) {
    return false;
  }
  
  // Verifica se todos os dígitos são iguais
  if (numbers.split('').every(digit => digit === numbers[0])) {
    return false;
  }
  
  // Validação dos dígitos verificadores
  const digits = numbers.split('').map(Number);
  
  // Primeiro dígito verificador
  let sum = 0;
  let weight = 5;
  for (let i = 0; i < 12; i++) {
    sum += digits[i] * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  const firstDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  
  if (digits[12] !== firstDigit) {
    return false;
  }
  
  // Segundo dígito verificador
  sum = 0;
  weight = 6;
  for (let i = 0; i < 13; i++) {
    sum += digits[i] * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  const secondDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  
  return digits[13] === secondDigit;
}

/**
 * Valida número de telefone brasileiro
 */
function validatePhone(value: string): boolean {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(value);
}

/**
 * Valida email
 */
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// ============================================================================
// UTILITÁRIOS DE FORMATAÇÃO
// ============================================================================

/**
 * Formata valor no formato TLV (Tag-Length-Value)
 */
function getValue(identify: string, value: string): string {
  return `${identify}${value.length.toString().padStart(2, '0')}${value}`;
}

/**
 * Formata texto removendo caracteres especiais e normalizando
 */
function formattedText(value: string): string {
  // Normaliza caracteres unicode e remove acentos
  const normalized = value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  
  // Mantém apenas caracteres permitidos: A-Z, a-z, 0-9, $, @, %, *, +, -, ., /, :, _, espaço
  return normalized.replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '');
}

/**
 * Calcula CRC16-CCITT-FALSE para dados PIX
 */
function crcCompute(data: string): string {
  let crc = 0xFFFF;
  const dataBytes = Buffer.from(data, 'utf-8');

  for (const byte of dataBytes) {
    crc ^= (byte << 8);
    for (let i = 0; i < 8; i++) {
      if (crc & 0x8000) {
        crc = (crc << 1) ^ 0x1021;
      } else {
        crc <<= 1;
      }
    }
  }

  return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
}

// ============================================================================
// GERADOR DE PAYLOAD PIX
// ============================================================================

/**
 * Classe principal para geração de payload PIX
 */
export class PixPayloadGenerator {
  /**
   * Valida a configuração fornecida
   */
  private static validateConfig(config: PixPayloadConfig): void {
    // Validação de campos obrigatórios
    if (!config.key && !config.dynamicUrl) {
      throw new Error('É necessário fornecer uma chave PIX ou URL dinâmica.');
    }
    
    if (!config.nameReceiver) {
      throw new Error('Nome do beneficiário é obrigatório.');
    }
    
    if (!config.cityReceiver) {
      throw new Error('Cidade do beneficiário é obrigatória.');
    }
    
    // Validação de tamanhos
    if (config.nameReceiver.length > 25) {
      throw new Error('O nome do beneficiário deve ter no máximo 25 caracteres.');
    }
    
    if (config.cityReceiver.length > 15) {
      throw new Error('A cidade do beneficiário deve ter no máximo 15 caracteres.');
    }
    
    // Validação do valor
    if (config.amount !== undefined) {
      const formattedValue = config.amount.toFixed(2);
      if (formattedValue.length > 13) {
        throw new Error('O valor deve ter no máximo 13 caracteres incluindo decimais.');
      }
    }
    
    // Validação da chave PIX
    if (config.key) {
      switch (config.keyType) {
        case 'CPF':
          if (!validateCPF(config.key)) {
            throw new Error('CPF inválido.');
          }
          break;
        case 'CNPJ':
          if (!validateCNPJ(config.key)) {
            throw new Error('CNPJ inválido.');
          }
          break;
        case 'EMAIL':
          if (!validateEmail(config.key)) {
            throw new Error('Email inválido.');
          }
          break;
        case 'PHONE':
          if (!validatePhone(config.key)) {
            throw new Error('Telefone inválido.');
          }
          break;
        case 'RANDOM':
          // Chave aleatória não precisa de validação específica
          break;
        default:
          throw new Error('Tipo de chave PIX inválido.');
      }
    }
  }
  
  /**
   * Processa a chave PIX conforme o tipo
   */
  private static processKey(keyType: string, key: string): string {
    switch (keyType) {
      case 'CPF':
      case 'CNPJ':
        return key.replace(/\D/g, ''); // Remove formatação
      case 'PHONE':
        return key.startsWith('+55') ? key : `+55${key.replace(/\D/g, '')}`;
      case 'EMAIL':
      case 'RANDOM':
      default:
        return key;
    }
  }
  
  /**
   * Gera o campo de informações da conta (campo 26)
   */
  private static getAccountInformation(config: PixPayloadConfig): string {
    const basePix = getValue('00', 'br.gov.bcb.pix');
    let infoString = '';

    if (config.key) {
      const processedKey = this.processKey(config.keyType, config.key);
      infoString += getValue('01', processedKey);
    } else if (config.dynamicUrl) {
      const cleanUrl = config.dynamicUrl.replace('https://', '');
      infoString += getValue('25', cleanUrl);
    }

    if (config.description) {
      infoString += getValue('02', formattedText(config.description));
    }

    return getValue('26', basePix + infoString);
  }
  
  /**
   * Gera o campo de dados adicionais (campo 62)
   */
  private static getAdditionalDataField(config: PixPayloadConfig): string {
    const txid = config.identification ? formattedText(config.identification) : '***';
    return getValue('62', getValue('05', txid));
  }
  
  /**
   * Gera o payload PIX completo
   */
  static generatePayload(config: PixPayloadConfig): string {
    // Valida a configuração
    this.validateConfig(config);
    
    const amount = config.amount || 0;
    const amountStr = amount.toFixed(2);
    
    const resultString = [
      getValue('00', '01'), // Payload Format Indicator
      getValue('01', config.singleTransaction ? '12' : '11'), // Point of Initiation Method
      this.getAccountInformation(config),
      getValue('52', '0000'), // Merchant Category Code
      getValue('53', '986'), // Transaction Currency (BRL)
      config.key ? getValue('54', amountStr) : '', // Transaction Amount
      getValue('58', 'BR'), // Country Code
      getValue('59', formattedText(config.nameReceiver)), // Merchant Name
      getValue('60', formattedText(config.cityReceiver)), // Merchant City
      config.zipcodeReceiver ? getValue('61', formattedText(config.zipcodeReceiver)) : '', // Postal Code
      this.getAdditionalDataField(config),
      '6304' // CRC placeholder
    ].join('');

    return resultString + crcCompute(resultString);
  }
}

// ============================================================================
// FUNÇÕES DE CONVENIÊNCIA
// ============================================================================

/**
 * Função de conveniência para gerar payload PIX
 */
export function generatePixPayload(config: PixPayloadConfig): string {
  return PixPayloadGenerator.generatePayload(config);
}

/**
 * Função para gerar payload PIX com parâmetros individuais
 */
export function createPixPayload(
  keyType: 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE' | 'RANDOM',
  key: string,
  nameReceiver: string,
  cityReceiver: string,
  amount?: number,
  identification?: string,
  description?: string
): string {
  return generatePixPayload({
    keyType,
    key,
    nameReceiver,
    cityReceiver,
    amount,
    identification,
    description
  });
}
