{"name": "qr-code-styling", "version": "1.9.2", "description": "Add a style and an image to your QR code", "main": "lib/qr-code-styling.js", "types": "lib/index.d.ts", "files": ["lib"], "engines": {"node": ">=18.18.0"}, "dependencies": {"qrcode-generator": "^1.4.4"}, "devDependencies": {"@eslint/js": "^9.12.0", "@types/eslint__js": "^8.42.3", "@types/jsdom": "^21.1.7", "ajv": "^8.17.1", "canvas": "^3.0.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.8.3", "eslint-webpack-plugin": "^4.2.0", "filemanager-webpack-plugin": "^8.0.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.1.2", "jest-fixed-jsdom": "^0.0.4", "jsdom": "^26.1.0", "playwright": "^1.55.0", "prettier": "^3.3.3", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "typescript": "^5.6.3", "typescript-eslint": "^8.8.1", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-merge": "^6.0.1"}, "scripts": {"build": "webpack --mode=production --config webpack.config.build.js", "build:dev": "webpack --mode=development --config webpack.config.build.js", "test": "jest", "start": "webpack serve --open --config webpack.config.dev-server.js", "dev": "webpack serve --config webpack.config.dev-server.js", "ci:versionup:patch": "npm version patch --no-git-tag-version --yes && npm run commit-version", "ci:versionup:minor": "npm version minor --no-git-tag-version --yes && npm run commit-version", "ci:versionup:major": "npm version major --no-git-tag-version --yes && npm run commit-version", "commit-version": "git add . && git commit -m \"chore(release): v`node -p 'require(\"./package.json\").version'`\"", "ci:release": "npm publish --yes"}, "repository": {"type": "git", "url": "git+https://github.com/kozakdenys/qr-code-styling.git"}, "keywords": ["qr", "qrcode", "qr-code", "js", "qrjs", "qrstyling", "styling", "qrbranding", "branding", "qrimage", "image", "qrlogo", "logo", "design"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/kozakdenys/qr-code-styling/issues"}, "homepage": "https://qr-code-styling.com"}