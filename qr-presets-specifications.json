{"library": {"name": "qr-code-styling", "version": "1.9.2", "repository": "https://github.com/kozakdenys/qr-code-styling", "dependencies": {"qrcode-generator": "^1.4.4"}}, "configuration": {"defaultOptions": {"width": 300, "height": 300, "type": "svg", "margin": 10, "qrOptions": {"typeNumber": 0, "mode": null, "errorCorrectionLevel": "M"}, "roundSize": true}}, "presets": {"modern": {"name": "Moderno", "description": "Design fluido e contemporâneo com gradação de roxos", "icon": "🎨", "parameters": {"dotsType": "rounded", "dotsColor": "#667eea", "cornerSquareType": "extra-rounded", "cornerSquareColor": "#764ba2", "cornerDotType": "rounded", "cornerDotColor": "#667eea", "backgroundColor": "#ffffff"}, "characteristics": {"style": "fluid", "colorScheme": "purple gradient", "shapes": "organic", "bestFor": ["tech", "startups", "modern brands"]}}, "classic": {"name": "Clássico", "description": "QR Code tradicional monocromático de alto contraste", "icon": "📱", "parameters": {"dotsType": "square", "dotsColor": "#000000", "cornerSquareType": "square", "cornerSquareColor": "#000000", "cornerDotType": "square", "cornerDotColor": "#000000", "backgroundColor": "#ffffff"}, "characteristics": {"style": "traditional", "colorScheme": "monochrome", "shapes": "geometric", "bestFor": ["universal", "print", "maximum compatibility"]}}, "elegant": {"name": "Elegante", "description": "Paleta monocromática refinada com contraste suave", "icon": "✨", "parameters": {"dotsType": "classy-rounded", "dotsColor": "#2c3e50", "cornerSquareType": "rounded", "cornerSquareColor": "#34495e", "cornerDotType": "rounded", "cornerDotColor": "#2c3e50", "backgroundColor": "#ecf0f1"}, "characteristics": {"style": "refined", "colorScheme": "grayscale", "shapes": "balanced", "bestFor": ["premium", "luxury", "professional"]}}, "vibrant": {"name": "<PERSON>ib<PERSON><PERSON>", "description": "Paleta multicolorida energética com formas circulares", "icon": "🌈", "parameters": {"dotsType": "dots", "dotsColor": "#e74c3c", "cornerSquareType": "extra-rounded", "cornerSquareColor": "#3498db", "cornerDotType": "dot", "cornerDotColor": "#f39c12", "backgroundColor": "#ffffff"}, "characteristics": {"style": "energetic", "colorScheme": "multicolor", "shapes": "circular", "bestFor": ["youth", "creative", "dynamic brands"]}}, "circular": {"name": "Circular", "description": "Design com predominância de formas circulares", "icon": "⭕", "parameters": {"dotsType": "rounded", "dotsColor": "#2c3e50", "cornerSquareType": "dot", "cornerSquareColor": "#e74c3c", "cornerDotType": "dot", "cornerDotColor": "#3498db", "backgroundColor": "#ffffff"}, "characteristics": {"style": "modern", "colorScheme": "tricolor", "shapes": "circular", "bestFor": ["friendly", "approachable", "modern"]}}}, "shapeTypes": {"dots": {"square": "Square shape with sharp corners", "rounded": "Square with rounded corners", "dots": "Perfect circle", "classy": "Square with corner cutouts", "classy-rounded": "Classy with rounded edges", "extra-rounded": "Ultra-rounded, almost circular"}, "cornerSquare": {"square": "Standard square", "rounded": "Rounded corners", "extra-rounded": "Ultra-rounded", "dot": "Circular", "classy": "With cutouts", "classy-rounded": "Rounded cutouts"}, "cornerDot": {"square": "Square shape", "dot": "Circular shape", "rounded": "Rounded square", "classy": "With cutouts", "classy-rounded": "Rounded cutouts", "extra-rounded": "Ultra-rounded"}}, "implementation": {"files": {"presetDefinition": "/public/js/app.js:2007-2053", "applyFunction": "/public/js/app.js:2006-2072", "htmlInterface": "/public/index.html:113-119", "cssStyles": "/public/css/styles.css:566-589"}, "domElements": {"dotsType": "dots-type", "dotsColor": "dots-color", "cornerSquareType": "corner-square-type", "cornerSquareColor": "corner-square-color", "cornerDotType": "corner-dot-type", "cornerDotColor": "corner-dot-color", "backgroundColor": "background-color"}}, "colorPalette": {"purple": {"primary": "#667eea", "secondary": "#764ba2"}, "gray": {"dark": "#2c3e50", "medium": "#34495e", "light": "#ecf0f1"}, "vibrant": {"red": "#e74c3c", "blue": "#3498db", "orange": "#f39c12"}, "base": {"white": "#ffffff", "black": "#000000"}}, "renderingPipeline": {"steps": [{"order": 1, "action": "applyPreset", "description": "User selects preset from UI"}, {"order": 2, "action": "updateDOMElements", "description": "Update form inputs with preset values"}, {"order": 3, "action": "getStandardQROptions", "description": "Build QRCodeStyling options object"}, {"order": 4, "action": "createQRCodeInstance", "description": "Instantiate new QRCodeStyling with options"}, {"order": 5, "action": "append<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Render QR code to DOM container"}, {"order": 6, "action": "postProcessing", "description": "Apply margin adjustments if needed"}]}}