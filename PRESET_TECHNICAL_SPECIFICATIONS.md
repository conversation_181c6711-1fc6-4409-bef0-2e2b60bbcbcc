# Especificações Técnicas dos Presets de QR Code

## Sumário Executivo

Este documento detalha as especificações técnicas completas dos cinco presets de personalização de QR Code disponíveis no QR Code Generator. A aplicação utiliza a biblioteca **qr-code-styling** (versão 1.9.2) para renderização avançada de QR codes com suporte a formas personalizadas e gradientes.

## Biblioteca e Tecnologia

### Biblioteca Principal
- **Nome:** qr-code-styling
- **Versão:** 1.9.2
- **Core Dependency:** qrcode-generator ^1.4.4
- **Rendering:** SVG e Canvas
- **Localização:** `/lib/qr-code-styling.js`

### Integração no Projeto
- **Arquivo de Implementação:** `/public/js/app.js`
- **Linha de Definição dos Presets:** 2007-2053
- **Função de Aplicação:** `applyPreset(presetName)` (linha 2006)
- **HTML Interface:** `/public/index.html` (linhas 113-119)

## Tabela Comparativa de Presets

| Parâmetro | Moderno | Clássico | Elegante | Vibrante | Circular |
|-----------|---------|----------|----------|----------|----------|
| **dotsType** | `rounded` | `square` | `classy-rounded` | `dots` | `rounded` |
| **dotsColor** | `#667eea` | `#000000` | `#2c3e50` | `#e74c3c` | `#2c3e50` |
| **cornerSquareType** | `extra-rounded` | `square` | `rounded` | `extra-rounded` | `dot` |
| **cornerSquareColor** | `#764ba2` | `#000000` | `#34495e` | `#3498db` | `#e74c3c` |
| **cornerDotType** | `rounded` | `square` | `rounded` | `dot` | `dot` |
| **cornerDotColor** | `#667eea` | `#000000` | `#2c3e50` | `#f39c12` | `#3498db` |
| **backgroundColor** | `#ffffff` | `#ffffff` | `#ecf0f1` | `#ffffff` | `#ffffff` |

## Especificações Detalhadas por Preset

### 1. Preset Moderno (Modern)
```javascript
modern: {
  dotsType: 'rounded',           // Pontos arredondados para aparência suave
  dotsColor: '#667eea',          // Roxo médio (tom moderno e tech)
  cornerSquareType: 'extra-rounded', // Cantos ultra-arredondados
  cornerSquareColor: '#764ba2',  // Roxo escuro (contraste)
  cornerDotType: 'rounded',       // Centro arredondado
  cornerDotColor: '#667eea',      // Mesma cor dos pontos
  backgroundColor: '#ffffff',     // Fundo branco padrão
}
```

**Características Visuais:**
- Design fluido e contemporâneo
- Gradação de roxos criando profundidade
- Cantos suaves com formas orgânicas
- Ideal para aplicações tech e startups

### 2. Preset Clássico (Classic)
```javascript
classic: {
  dotsType: 'square',            // Pontos quadrados tradicionais
  dotsColor: '#000000',          // Preto puro
  cornerSquareType: 'square',    // Cantos quadrados
  cornerSquareColor: '#000000',  // Preto puro
  cornerDotType: 'square',       // Centro quadrado
  cornerDotColor: '#000000',     // Preto puro
  backgroundColor: '#ffffff',     // Fundo branco
}
```

**Características Visuais:**
- QR Code tradicional monocromático
- Alto contraste para máxima legibilidade
- Formas geométricas precisas
- Compatibilidade universal com leitores

### 3. Preset Elegante (Elegant)
```javascript
elegant: {
  dotsType: 'classy-rounded',    // Pontos com arredondamento clássico
  dotsColor: '#2c3e50',          // Cinza escuro azulado
  cornerSquareType: 'rounded',   // Cantos arredondados
  cornerSquareColor: '#34495e',  // Cinza escuro (tom mais forte)
  cornerDotType: 'rounded',      // Centro arredondado
  cornerDotColor: '#2c3e50',     // Mesma cor dos pontos
  backgroundColor: '#ecf0f1',    // Cinza claro (não é branco puro)
}
```

**Características Visuais:**
- Paleta monocromática refinada
- Contraste suave entre fundo e elementos
- Formas balanceadas entre orgânico e geométrico
- Adequado para marcas premium

### 4. Preset Vibrante (Vibrant)
```javascript
vibrant: {
  dotsType: 'dots',              // Pontos circulares
  dotsColor: '#e74c3c',          // Vermelho vibrante
  cornerSquareType: 'extra-rounded', // Cantos ultra-arredondados
  cornerSquareColor: '#3498db',  // Azul vibrante
  cornerDotType: 'dot',          // Centro circular
  cornerDotColor: '#f39c12',     // Laranja/Amarelo vibrante
  backgroundColor: '#ffffff',     // Fundo branco
}
```

**Características Visuais:**
- Paleta multicolorida energética
- Cada elemento com cor distinta
- Formas circulares e orgânicas
- Ideal para marcas jovens e dinâmicas

### 5. Preset Circular
```javascript
circular: {
  dotsType: 'rounded',           // Pontos arredondados
  dotsColor: '#2c3e50',          // Cinza escuro azulado
  cornerSquareType: 'dot',       // Cantos circulares
  cornerSquareColor: '#e74c3c',  // Vermelho (destaque)
  cornerDotType: 'dot',          // Centro circular
  cornerDotColor: '#3498db',     // Azul (contraste)
  backgroundColor: '#ffffff',     // Fundo branco
}
```

**Características Visuais:**
- Predominância de formas circulares
- Contraste colorido nos elementos de posicionamento
- Design balanceado com foco em curvas
- Aparência moderna e amigável

## Tipos de Formas Disponíveis

### DotsType (Pontos de Dados)
```typescript
type DotType = 
  | "square"          // Quadrado padrão
  | "rounded"         // Quadrado com bordas arredondadas
  | "dots"            // Círculo perfeito
  | "classy"          // Quadrado com recortes nos cantos
  | "classy-rounded"  // Classy com bordas arredondadas
  | "extra-rounded"   // Ultra-arredondado (quase circular)
```

### CornerSquareType (Quadrados de Posição)
```typescript
type CornerSquareType = 
  | "square"          // Quadrado padrão
  | "rounded"         // Bordas arredondadas
  | "extra-rounded"   // Ultra-arredondado
  | "dot"             // Circular
  | "classy"          // Com recortes
  | "classy-rounded"  // Recortes arredondados
```

### CornerDotType (Pontos Centrais)
```typescript
type CornerDotType = 
  | "square"          // Quadrado
  | "dot"             // Circular
  | "rounded"         // Arredondado
  | "classy"          // Com recortes
  | "classy-rounded"  // Recortes arredondados
  | "extra-rounded"   // Ultra-arredondado
```

## Pipeline de Renderização

### 1. Aplicação do Preset
```javascript
function applyPreset(presetName) {
  const presets = { /* definições */ };
  const preset = presets[presetName];
  
  if (preset) {
    // Atualiza elementos DOM com valores do preset
    document.getElementById('dots-type').value = preset.dotsType;
    document.getElementById('dots-color').value = preset.dotsColor;
    document.getElementById('corner-square-type').value = preset.cornerSquareType;
    document.getElementById('corner-square-color').value = preset.cornerSquareColor;
    document.getElementById('corner-dot-type').value = preset.cornerDotType;
    document.getElementById('corner-dot-color').value = preset.cornerDotColor;
    document.getElementById('background-color').value = preset.backgroundColor;
    
    // Regenera QR se já houver dados
    if (window.pixGenerator && window.pixGenerator.currentBRCode) {
      window.pixGenerator.generateQRIfReady();
    }
  }
}
```

### 2. Configuração do QR Code Styling
```javascript
getStandardQROptions(data, width, height, margin, type = 'svg') {
  const baseOptions = {
    width: actualWidth,
    height: actualHeight,
    type: type,
    data: data,
    margin: margin,
    qrOptions: {
      typeNumber: 0,        // Auto-detect
      mode: undefined,
      errorCorrectionLevel: 'M',  // Nível médio de correção
    },
    dotsOptions: {
      color: document.getElementById('dots-color').value || '#000000',
      type: document.getElementById('dots-type').value || 'square',
      roundSize: true,      // Sempre habilitado para qualidade
    },
    backgroundOptions: {
      color: document.getElementById('background-color').value || '#ffffff',
      round: 0,
    },
    cornersSquareOptions: {
      color: document.getElementById('corner-square-color').value || '#000000',
      type: document.getElementById('corner-square-type').value || undefined,
    },
    cornersDotOptions: {
      color: document.getElementById('corner-dot-color').value || '#000000',
      type: document.getElementById('corner-dot-type').value || undefined,
    },
  };
  
  return baseOptions;
}
```

### 3. Criação e Renderização
```javascript
createQRCode(options) {
  // Instancia novo QRCodeStyling com opções
  this.qrCode = new window.QRCodeStyling(options);
  
  // Limpa container de preview
  this.qrPreview.innerHTML = '';
  
  // Adiciona QR Code ao DOM
  this.qrCode.append(this.qrPreview);
  
  // Pós-processamento se necessário (margem zero, etc)
  if (options.margin === 0) {
    setTimeout(() => this.forceZeroMargin(), 100);
  }
}
```

## Implementação para Reprodução

### Instalação de Dependências
```bash
npm install qr-code-styling@1.9.2
```

### Estrutura HTML Necessária
```html
<!-- Botões de Preset -->
<div class="preset-buttons">
  <button onclick="applyPreset('modern')">🎨 Moderno</button>
  <button onclick="applyPreset('classic')">📱 Clássico</button>
  <button onclick="applyPreset('elegant')">✨ Elegante</button>
  <button onclick="applyPreset('vibrant')">🌈 Vibrante</button>
  <button onclick="applyPreset('circular')">⭕ Circular</button>
</div>

<!-- Controles de Personalização -->
<select id="dots-type">...</select>
<input type="color" id="dots-color" />
<select id="corner-square-type">...</select>
<input type="color" id="corner-square-color" />
<select id="corner-dot-type">...</select>
<input type="color" id="corner-dot-color" />
<input type="color" id="background-color" />
```

### Exemplo de Uso Completo
```javascript
// Importar biblioteca
import QRCodeStyling from "qr-code-styling";

// Aplicar preset Moderno
const modernOptions = {
  width: 300,
  height: 300,
  type: "svg",
  data: "https://example.com",
  dotsOptions: {
    color: "#667eea",
    type: "rounded"
  },
  cornersSquareOptions: {
    color: "#764ba2",
    type: "extra-rounded"
  },
  cornersDotOptions: {
    color: "#667eea",
    type: "rounded"
  },
  backgroundOptions: {
    color: "#ffffff"
  }
};

// Criar e renderizar
const qrCode = new QRCodeStyling(modernOptions);
qrCode.append(document.getElementById("qr-container"));
```

## Considerações Especiais

### 1. Nível de Correção de Erro
- Sempre configurado como **'M' (Medium)**
- Proporciona balanço entre redundância e densidade de dados
- Permite recuperação de até 15% de dados danificados

### 2. Tratamento de Margens
- **Margem 0:** Usa tamanho natural do QR (~270x270 para 300x300)
- **Margem 10:** Padrão recomendado
- **Margem 30:** Margem ampla para apresentações

### 3. Formatos de Exportação
- **SVG:** Sempre usado para exportação de alta qualidade
- **Canvas:** Usado para margens muito pequenas (≤1px)
- **PNG:** Gerado a partir do SVG via Canvas

### 4. Otimizações de Performance
- `roundSize: true` sempre habilitado para melhor anti-aliasing
- `crossOrigin: 'anonymous'` para imagens externas
- `saveAsBlob: true` para melhor qualidade de imagem

## Conclusão

Os presets fornecem configurações pré-definidas e testadas que garantem:
1. **Consistência Visual:** Cada preset mantém harmonia entre seus elementos
2. **Legibilidade:** Todos os presets mantêm contraste adequado para leitura
3. **Flexibilidade:** Usuários podem partir de um preset e personalizar
4. **Compatibilidade:** Funcionam com todos os leitores de QR Code padrão

Para restaurar a funcionalidade em uma aplicação refatorada, é essencial:
- Manter a mesma estrutura de objetos de configuração
- Usar a biblioteca qr-code-styling na mesma versão
- Preservar os IDs dos elementos DOM
- Implementar a função applyPreset() exatamente como documentada
