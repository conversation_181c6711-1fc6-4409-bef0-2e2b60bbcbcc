"use strict";
/**
 * Exemplos de uso do PIX Payload Generator
 *
 * Este arquivo demonstra como usar o módulo pix-payload-generator.ts
 * para gerar payloads PIX em diferentes cenários.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const pix_payload_generator_1 = require("./pix-payload-generator");
// ============================================================================
// EXEMPLOS DE USO
// ============================================================================
console.log('=== EXEMPLOS DE GERAÇÃO DE PAYLOAD PIX ===\n');
// Exemplo 1: PIX com chave CPF
console.log('1. PIX com chave CPF:');
try {
    const pixCPF = (0, pix_payload_generator_1.createPixPayload)('CPF', '12345678909', 'João Silva', 'São Paulo', 100.50, 'PAG001', 'Pagamento de serviços');
    console.log('Payload:', pixCPF);
    console.log('Tamanho:', pixCPF.length, 'caracteres\n');
}
catch (error) {
    console.error('Erro:', error.message, '\n');
}
// Exemplo 2: PIX com chave de telefone
console.log('2. PIX com chave de telefone:');
try {
    const pixPhone = (0, pix_payload_generator_1.generatePixPayload)({
        keyType: 'PHONE',
        key: '11999887766',
        nameReceiver: 'Maria Santos',
        cityReceiver: 'Rio de Janeiro',
        amount: 25.00,
        description: 'Transferência PIX'
    });
    console.log('Payload:', pixPhone);
    console.log('Tamanho:', pixPhone.length, 'caracteres\n');
}
catch (error) {
    console.error('Erro:', error.message, '\n');
}
// Exemplo 3: PIX com chave email
console.log('3. PIX com chave email:');
try {
    const pixEmail = (0, pix_payload_generator_1.generatePixPayload)({
        keyType: 'EMAIL',
        key: '<EMAIL>',
        nameReceiver: 'Pedro Costa',
        cityReceiver: 'Belo Horizonte',
        amount: 0, // Valor aberto
        identification: 'DOA001',
        description: 'Doação'
    });
    console.log('Payload:', pixEmail);
    console.log('Tamanho:', pixEmail.length, 'caracteres\n');
}
catch (error) {
    console.error('Erro:', error.message, '\n');
}
// Exemplo 4: PIX com chave aleatória
console.log('4. PIX com chave aleatória:');
try {
    const pixRandom = (0, pix_payload_generator_1.generatePixPayload)({
        keyType: 'RANDOM',
        key: 'b5fe1edc-d108-410f-b966-eccaaca75e4f',
        nameReceiver: 'Ana Oliveira',
        cityReceiver: 'Brasília',
        amount: 150.75,
        zipcodeReceiver: '70040010',
        identification: 'VEN001',
        description: 'Venda de produto'
    });
    console.log('Payload:', pixRandom);
    console.log('Tamanho:', pixRandom.length, 'caracteres\n');
}
catch (error) {
    console.error('Erro:', error.message, '\n');
}
// Exemplo 5: PIX com CNPJ
console.log('5. PIX com chave CNPJ:');
try {
    const pixCNPJ = (0, pix_payload_generator_1.generatePixPayload)({
        keyType: 'CNPJ',
        key: '11222333000181',
        nameReceiver: 'Empresa LTDA',
        cityReceiver: 'Curitiba',
        amount: 500.00,
        identification: 'FAT001',
        description: 'Pagamento de fatura'
    });
    console.log('Payload:', pixCNPJ);
    console.log('Tamanho:', pixCNPJ.length, 'caracteres\n');
}
catch (error) {
    console.error('Erro:', error.message, '\n');
}
// Exemplo 6: PIX dinâmico com URL
console.log('6. PIX dinâmico com URL:');
try {
    const pixDynamic = (0, pix_payload_generator_1.generatePixPayload)({
        keyType: 'RANDOM', // Não será usado pois temos dynamicUrl
        key: '', // Não será usado
        dynamicUrl: 'psp.exemplo.com/pagamento/123456',
        nameReceiver: 'Loja Virtual',
        cityReceiver: 'São Paulo',
        singleTransaction: true,
        identification: 'ORD001',
        description: 'Compra online'
    });
    console.log('Payload:', pixDynamic);
    console.log('Tamanho:', pixDynamic.length, 'caracteres\n');
}
catch (error) {
    console.error('Erro:', error.message, '\n');
}
// Exemplo 7: Tratamento de erros
console.log('7. Exemplo de tratamento de erros:');
try {
    const pixInvalid = (0, pix_payload_generator_1.generatePixPayload)({
        keyType: 'CPF',
        key: '12345678900', // CPF inválido
        nameReceiver: 'Nome muito longo que excede o limite de 25 caracteres permitidos',
        cityReceiver: 'São Paulo'
    });
    console.log('Payload:', pixInvalid);
}
catch (error) {
    console.error('Erro capturado:', error.message, '\n');
}
// ============================================================================
// FUNÇÃO PARA DEMONSTRAR VALIDAÇÕES
// ============================================================================
function demonstrarValidacoes() {
    console.log('=== DEMONSTRAÇÃO DE VALIDAÇÕES ===\n');
    const testCases = [
        {
            name: 'CPF válido',
            config: { keyType: 'CPF', key: '12345678909', nameReceiver: 'Test', cityReceiver: 'City' }
        },
        {
            name: 'CPF inválido',
            config: { keyType: 'CPF', key: '12345678900', nameReceiver: 'Test', cityReceiver: 'City' }
        },
        {
            name: 'Email válido',
            config: { keyType: 'EMAIL', key: '<EMAIL>', nameReceiver: 'Test', cityReceiver: 'City' }
        },
        {
            name: 'Email inválido',
            config: { keyType: 'EMAIL', key: 'email-invalido', nameReceiver: 'Test', cityReceiver: 'City' }
        },
        {
            name: 'Nome muito longo',
            config: { keyType: 'RANDOM', key: 'test-key', nameReceiver: 'Nome extremamente longo que excede limite', cityReceiver: 'City' }
        },
        {
            name: 'Cidade muito longa',
            config: { keyType: 'RANDOM', key: 'test-key', nameReceiver: 'Test', cityReceiver: 'Cidade com nome muito longo' }
        }
    ];
    testCases.forEach(testCase => {
        console.log(`Testando: ${testCase.name}`);
        try {
            const payload = (0, pix_payload_generator_1.generatePixPayload)(testCase.config);
            console.log('✅ Sucesso - Payload gerado');
        }
        catch (error) {
            console.log('❌ Erro:', error.message);
        }
        console.log('');
    });
}
// Executar demonstração de validações
demonstrarValidacoes();
// ============================================================================
// EXEMPLO DE INTEGRAÇÃO EM APLICAÇÃO
// ============================================================================
/**
 * Exemplo de como integrar o gerador em uma aplicação
 */
class PaymentService {
    static generatePixPayment(customerData, transactionData) {
        try {
            return (0, pix_payload_generator_1.generatePixPayload)({
                keyType: customerData.pixKeyType,
                key: customerData.pixKey,
                nameReceiver: customerData.name,
                cityReceiver: customerData.city,
                amount: transactionData.amount,
                identification: transactionData.orderId,
                description: transactionData.description
            });
        }
        catch (error) {
            throw new Error(`Falha ao gerar PIX: ${error.message}`);
        }
    }
}
// Exemplo de uso do serviço
console.log('=== EXEMPLO DE INTEGRAÇÃO ===\n');
try {
    const pixPayload = PaymentService.generatePixPayment({
        name: 'Comercio ABC',
        city: 'São Paulo',
        pixKey: '<EMAIL>',
        pixKeyType: 'EMAIL'
    }, {
        amount: 89.90,
        description: 'Compra de produtos',
        orderId: 'ORD-2024-001'
    });
    console.log('Payload PIX gerado para integração:');
    console.log(pixPayload);
    console.log('\nEste payload pode ser usado para gerar QR Code ou enviado diretamente para processamento.\n');
}
catch (error) {
    console.error('Erro na integração:', error.message);
}
