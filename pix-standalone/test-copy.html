<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .payload-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            word-break: break-all;
            margin-bottom: 15px;
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4a5568;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        .copy-btn:hover {
            background: #2d3748;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #c6f6d5;
            color: #22543d;
            border-left: 5px solid #38a169;
        }
        .error {
            background: #fed7d7;
            color: #c53030;
            border-left: 5px solid #e53e3e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste da Função Copiar Payload</h1>
        
        <p>Clique no botão "Copiar" abaixo para testar a funcionalidade:</p>
        
        <div class="payload-output">
            <button class="copy-btn" onclick="copyToClipboard('00020101021126330014br.gov.bcb.pix01112830474384152040000530398654041.455802BR5910Wellington6006Franca62070503***63046217', this)">Copiar</button>
            00020101021126330014br.gov.bcb.pix01112830474384152040000530398654041.455802BR5910Wellington6006Franca62070503***63046217
        </div>
        
        <div id="result"></div>
        
        <h3>Como testar:</h3>
        <ol>
            <li>Clique no botão "Copiar" acima</li>
            <li>O botão deve mudar para "Copiado!" temporariamente</li>
            <li>Cole o conteúdo em qualquer lugar (Ctrl+V) para verificar se foi copiado</li>
        </ol>
    </div>

    <script>
        // Função para copiar payload
        window.copyToClipboard = function(text, buttonElement) {
            console.log('🔄 Tentando copiar:', text.substring(0, 50) + '...');
            console.log('🎯 Botão:', buttonElement);
            
            if (!navigator.clipboard) {
                showResult('❌ Erro: Clipboard API não disponível neste navegador', 'error');
                return;
            }
            
            navigator.clipboard.writeText(text)
                .then(function() {
                    console.log('✅ Texto copiado com sucesso!');
                    
                    // Feedback visual
                    const btn = buttonElement;
                    const originalText = btn.textContent;
                    const originalBackground = btn.style.background;
                    
                    btn.textContent = 'Copiado!';
                    btn.style.background = '#38a169';
                    
                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.background = originalBackground || '#4a5568';
                    }, 2000);
                    
                    showResult('✅ Payload copiado com sucesso! Cole em qualquer lugar para verificar.', 'success');
                })
                .catch(function(err) {
                    console.error('❌ Erro ao copiar:', err);
                    showResult('❌ Erro ao copiar: ' + err.message, 'error');
                });
        };
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="test-result ${type}">${message}</div>`;
            
            // Limpar mensagem após 5 segundos
            setTimeout(() => {
                resultDiv.innerHTML = '';
            }, 5000);
        }
        
        // Teste inicial
        console.log('🚀 Página de teste carregada');
        console.log('📋 Clipboard API disponível:', !!navigator.clipboard);
    </script>
</body>
</html>
