# 🚀 Instruções de Uso - PIX Payload Generator Standalone

## 📋 O que foi criado

Foi criado um módulo **completamente independente** para geração de payload PIX com interface web para testes. Todos os arquivos estão no diretório `pix-standalone/`.

## 🎯 Como testar agora

### 1. Iniciar o servidor local

```bash
cd pix-standalone
node server.js
```

**Saída esperada:**
```
🚀 PIX Payload Generator Server iniciado!
📍 Servidor rodando em: http://localhost:9876
📁 Servindo arquivos do diretório: /caminho/para/pix-standalone

📋 Arquivos disponíveis:
   • http://localhost:9876/ - Página principal com formulário
   • http://localhost:9876/PIX-PAYLOAD-README.md - Documentação
   • http://localhost:9876/pix-payload-generator.js - <PERSON><PERSON><PERSON><PERSON> PIX

⏹️  Para parar o servidor, pressione Ctrl+C
```

### 2. Abrir no navegador

Acesse: **http://localhost:9876**

### 3. Testar a interface

A página contém um formulário completo com:

#### Campos Obrigatórios:
- **Tipo de Chave PIX**: CPF, CNPJ, Email, Telefone, Chave Aleatória
- **Chave PIX**: O valor da chave (muda conforme o tipo)
- **Nome do Beneficiário**: Máximo 25 caracteres
- **Cidade do Beneficiário**: Máximo 15 caracteres

#### Campos Opcionais:
- **Valor da Transação**: Deixe vazio para valor aberto
- **CEP do Beneficiário**: Apenas números
- **Identificador da Transação**: Referência interna
- **Descrição da Transação**: Descrição do pagamento
- **Transação Única**: Checkbox para PIX dinâmico

## 🧪 Exemplos de Teste

### Teste 1: PIX com CPF
```
Tipo de Chave: CPF
Chave PIX: 12345678909
Nome: João Silva
Cidade: São Paulo
Valor: 100.50
Descrição: Pagamento de serviços
```

### Teste 2: PIX com Email (Valor Aberto)
```
Tipo de Chave: EMAIL
Chave PIX: <EMAIL>
Nome: Maria Santos
Cidade: Rio de Janeiro
Valor: (deixar vazio)
Descrição: Doação livre
```

### Teste 3: PIX com Telefone
```
Tipo de Chave: PHONE
Chave PIX: 11999887766
Nome: Pedro Costa
Cidade: Belo Horizonte
Valor: 25.00
```

### Teste 4: PIX com Chave Aleatória
```
Tipo de Chave: RANDOM
Chave PIX: b5fe1edc-d108-410f-b966-eccaaca75e4f
Nome: Ana Oliveira
Cidade: Brasília
Valor: 150.75
CEP: 70040010
```

## ✅ O que esperar

### Resultado de Sucesso:
- ✅ **Payload PIX completo** em formato copiável
- ✅ **Informações do payload** (tamanho, tipo, valor)
- ✅ **Botão de cópia** para área de transferência
- ✅ **Validação visual** de sucesso

### Exemplo de Payload Gerado:
```
00020101021126580014br.gov.bcb.pix0111123456789090221Pagamento de servicos5204000053039865406100.505802BR5910Joao Silva6009Sao Paulo62100506PAG0016304A0B2
```

### Resultado de Erro:
- ❌ **Mensagem de erro clara** explicando o problema
- ❌ **Validações específicas** para cada tipo de chave
- ❌ **Feedback visual** em vermelho

## 🔧 Testes Programáticos

### Teste Rápido em JavaScript:
```bash
cd pix-standalone
node test-pix-payload.js
```

### Exemplos Completos:
```bash
cd pix-standalone
node pix-payload-example.js
```

## 📁 Estrutura dos Arquivos

```
pix-standalone/
├── 🌐 index.html                    # Interface web com formulário
├── ⚙️  server.js                     # Servidor HTTP local
├── 📦 pix-payload-generator.ts      # Módulo principal (TypeScript)
├── 📦 pix-payload-generator.js      # Módulo principal (JavaScript)
├── 📝 pix-payload-example.ts        # Exemplos de uso
├── 🧪 test-pix-payload.js           # Testes funcionais
├── 📚 PIX-PAYLOAD-README.md         # Documentação técnica
├── 📋 EXTRAÇÃO-PIX-RESUMO.md        # Resumo da extração
├── 📖 README.md                     # Documentação do diretório
└── 📋 INSTRUÇÕES-DE-USO.md          # Este arquivo
```

## 🎨 Características da Interface

### Design:
- ✨ **Interface moderna** com gradientes e animações
- 🎨 **Cores do PIX** (verde) como tema principal
- 📱 **Responsiva** para celular e desktop
- ♿ **Acessível** com labels e ajuda contextual

### Funcionalidades:
- 🔄 **Placeholders dinâmicos** baseados no tipo de chave
- ✅ **Validação em tempo real** dos campos
- 📋 **Cópia automática** do payload gerado
- 💡 **Ajuda contextual** para cada campo
- 🚨 **Tratamento de erros** com mensagens claras

## 🛠️ Uso em Outros Projetos

### Copiar apenas o módulo:
```javascript
// Copie apenas o arquivo: pix-payload-generator.js
const { generatePixPayload } = require('./pix-payload-generator.js');

const payload = generatePixPayload({
  keyType: 'CPF',
  key: '12345678909',
  nameReceiver: 'João Silva',
  cityReceiver: 'São Paulo',
  amount: 100.50
});
```

### Integração TypeScript:
```typescript
import { generatePixPayload } from './pix-payload-generator';
// Use normalmente...
```

## 🔍 Validações Implementadas

### Automáticas:
- ✅ **CPF**: Validação completa com dígitos verificadores
- ✅ **CNPJ**: Validação completa com dígitos verificadores
- ✅ **Email**: Validação de formato básico
- ✅ **Telefone**: Formatação automática com +55
- ✅ **Campos obrigatórios**: Nome e cidade
- ✅ **Tamanhos máximos**: Nome (25), cidade (15)
- ✅ **Formatação de texto**: Remove acentos automaticamente

### Estrutura do Payload:
- ✅ **Padrão EMV QR Code** do Banco Central
- ✅ **CRC16-CCITT-FALSE** calculado corretamente
- ✅ **Campos obrigatórios** conforme especificação PIX
- ✅ **Compatibilidade total** com bancos e PSPs

## 🎉 Pronto para Usar!

O módulo está **100% funcional** e pode ser usado imediatamente:

1. **Para testes**: Use a interface web em http://localhost:9876
2. **Para desenvolvimento**: Importe o módulo `pix-payload-generator.js`
3. **Para produção**: Copie apenas o arquivo do módulo para seu projeto

**Não há dependências externas** - o módulo é completamente independente! 🚀
