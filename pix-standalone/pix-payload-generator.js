/**
 * PIX Payload Generator - Módulo independente para geração de payload PIX
 *
 * Este módulo contém apenas a lógica necessária para gerar o payload PIX (BR-Code)
 * seguindo o padrão EMV QR Code do Banco Central do Brasil.
 *
 * Não inclui funcionalidades de geração de QR Code ou renderização visual.
 */
// ============================================================================
// UTILITÁRIOS DE VALIDAÇÃO
// ============================================================================
/**
 * Valida CPF brasileiro
 */
function validateCPF(numbers) {
  const cpf = numbers.replace(/\D/g, '').split('').map(Number);
  if (cpf.length !== 11) {
    return false;
  }
  // Verifica se todos os dígitos são iguais
  if (cpf.every((digit) => digit === cpf[0])) {
    return false;
  }
  // Valida dígitos verificadores
  for (let i = 9; i < 11; i++) {
    const value = cpf.slice(0, i).reduce((sum, digit, index) => {
      return sum + digit * (i + 1 - index);
    }, 0);
    const digit = ((value * 10) % 11) % 10;
    if (digit !== cpf[i]) {
      return false;
    }
  }
  return true;
}
/**
 * Valida CNPJ brasileiro
 */
function validateCNPJ(cnpj) {
  const numbers = cnpj.replace(/\D/g, '');
  if (numbers.length !== 14) {
    return false;
  }
  // Verifica se todos os dígitos são iguais
  if (numbers.split('').every((digit) => digit === numbers[0])) {
    return false;
  }
  // Validação dos dígitos verificadores
  const digits = numbers.split('').map(Number);
  // Primeiro dígito verificador
  let sum = 0;
  let weight = 5;
  for (let i = 0; i < 12; i++) {
    sum += digits[i] * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  const firstDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (digits[12] !== firstDigit) {
    return false;
  }
  // Segundo dígito verificador
  sum = 0;
  weight = 6;
  for (let i = 0; i < 13; i++) {
    sum += digits[i] * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  const secondDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  return digits[13] === secondDigit;
}
/**
 * Valida número de telefone brasileiro
 */
function validatePhone(value) {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(value);
}
/**
 * Valida email
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
// ============================================================================
// UTILITÁRIOS DE FORMATAÇÃO
// ============================================================================
/**
 * Formata valor no formato TLV (Tag-Length-Value)
 */
function getValue(identify, value) {
  return `${identify}${value.length.toString().padStart(2, '0')}${value}`;
}
/**
 * Formata texto removendo caracteres especiais e normalizando
 */
function formattedText(value) {
  // Normaliza caracteres unicode e remove acentos
  const normalized = value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  // Mantém apenas caracteres permitidos: A-Z, a-z, 0-9, $, @, %, *, +, -, ., /, :, _, espaço
  return normalized.replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '');
}
/**
 * Calcula CRC16-CCITT-FALSE para dados PIX
 */
function crcCompute(data) {
  let crc = 0xffff;
  const dataBytes = Buffer.from(data, 'utf-8');
  for (const byte of dataBytes) {
    crc ^= byte << 8;
    for (let i = 0; i < 8; i++) {
      if (crc & 0x8000) {
        crc = (crc << 1) ^ 0x1021;
      } else {
        crc <<= 1;
      }
    }
  }
  return (crc & 0xffff).toString(16).toUpperCase().padStart(4, '0');
}
// ============================================================================
// GERADOR DE PAYLOAD PIX
// ============================================================================
/**
 * Classe principal para geração de payload PIX
 */
class PixPayloadGenerator {
  /**
   * Valida a configuração fornecida
   */
  static validateConfig(config) {
    // Validação de campos obrigatórios
    if (!config.key && !config.dynamicUrl) {
      throw new Error('É necessário fornecer uma chave PIX ou URL dinâmica.');
    }
    if (!config.nameReceiver) {
      throw new Error('Nome do beneficiário é obrigatório.');
    }
    if (!config.cityReceiver) {
      throw new Error('Cidade do beneficiário é obrigatória.');
    }
    // Validação de tamanhos
    if (config.nameReceiver.length > 25) {
      throw new Error(
        'O nome do beneficiário deve ter no máximo 25 caracteres.'
      );
    }
    if (config.cityReceiver.length > 15) {
      throw new Error(
        'A cidade do beneficiário deve ter no máximo 15 caracteres.'
      );
    }
    // Validação do valor
    if (config.amount !== undefined) {
      const formattedValue = config.amount.toFixed(2);
      if (formattedValue.length > 13) {
        throw new Error(
          'O valor deve ter no máximo 13 caracteres incluindo decimais.'
        );
      }
    }
    // Validação da chave PIX
    if (config.key) {
      switch (config.keyType) {
        case 'CPF':
          if (!validateCPF(config.key)) {
            throw new Error('CPF inválido.');
          }
          break;
        case 'CNPJ':
          if (!validateCNPJ(config.key)) {
            throw new Error('CNPJ inválido.');
          }
          break;
        case 'EMAIL':
          if (!validateEmail(config.key)) {
            throw new Error('Email inválido.');
          }
          break;
        case 'PHONE':
          if (!validatePhone(config.key)) {
            throw new Error('Telefone inválido.');
          }
          break;
        case 'RANDOM':
          // Chave aleatória não precisa de validação específica
          break;
        default:
          throw new Error('Tipo de chave PIX inválido.');
      }
    }
  }
  /**
   * Processa a chave PIX conforme o tipo
   */
  static processKey(keyType, key) {
    switch (keyType) {
      case 'CPF':
      case 'CNPJ':
        return key.replace(/\D/g, ''); // Remove formatação
      case 'PHONE':
        return key.startsWith('+55') ? key : `+55${key.replace(/\D/g, '')}`;
      case 'EMAIL':
      case 'RANDOM':
      default:
        return key;
    }
  }
  /**
   * Gera o campo de informações da conta (campo 26)
   */
  static getAccountInformation(config) {
    const basePix = getValue('00', 'br.gov.bcb.pix');
    let infoString = '';
    if (config.key) {
      const processedKey = this.processKey(config.keyType, config.key);
      infoString += getValue('01', processedKey);
    } else if (config.dynamicUrl) {
      const cleanUrl = config.dynamicUrl.replace('https://', '');
      infoString += getValue('25', cleanUrl);
    }
    if (config.description) {
      infoString += getValue('02', formattedText(config.description));
    }
    return getValue('26', basePix + infoString);
  }
  /**
   * Gera o campo de dados adicionais (campo 62)
   */
  static getAdditionalDataField(config) {
    const txid = config.identification
      ? formattedText(config.identification)
      : '***';
    return getValue('62', getValue('05', txid));
  }
  /**
   * Gera o payload PIX completo
   */
  static generatePayload(config) {
    // Valida a configuração
    this.validateConfig(config);
    const amount = config.amount || 0;
    const amountStr = amount.toFixed(2);
    const resultString = [
      getValue('00', '01'), // Payload Format Indicator
      getValue('01', config.singleTransaction ? '12' : '11'), // Point of Initiation Method
      this.getAccountInformation(config),
      getValue('52', '0000'), // Merchant Category Code
      getValue('53', '986'), // Transaction Currency (BRL)
      config.key ? getValue('54', amountStr) : '', // Transaction Amount
      getValue('58', 'BR'), // Country Code
      getValue('59', formattedText(config.nameReceiver)), // Merchant Name
      getValue('60', formattedText(config.cityReceiver)), // Merchant City
      config.zipcodeReceiver
        ? getValue('61', formattedText(config.zipcodeReceiver))
        : '', // Postal Code
      this.getAdditionalDataField(config),
      '6304', // CRC placeholder
    ].join('');
    return resultString + crcCompute(resultString);
  }
}

// ============================================================================
// FUNÇÕES DE CONVENIÊNCIA
// ============================================================================
/**
 * Função de conveniência para gerar payload PIX
 */
function generatePixPayload(config) {
  return PixPayloadGenerator.generatePayload(config);
}
/**
 * Função para gerar payload PIX com parâmetros individuais
 */
function createPixPayload(
  keyType,
  key,
  nameReceiver,
  cityReceiver,
  amount,
  identification,
  description
) {
  return generatePixPayload({
    keyType,
    key,
    nameReceiver,
    cityReceiver,
    amount,
    identification,
    description,
  });
}

// ============================================================================
// EXPORTAÇÕES ES6
// ============================================================================

export { PixPayloadGenerator, generatePixPayload, createPixPayload };
