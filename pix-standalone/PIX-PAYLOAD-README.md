# PIX Payload Generator

Mó<PERSON>lo TypeScript/JavaScript independente para geração de payload PIX (BR-Code) seguindo o padrão EMV QR Code do Banco Central do Brasil.

## 📋 Características

- ✅ **Independente**: Não possui dependências externas
- ✅ **Completo**: Implementa todas as validações necessárias
- ✅ **Compatível**: Segue os padrões do Banco Central do Brasil
- ✅ **Flexível**: Suporta todos os tipos de chave PIX
- ✅ **Validado**: Inclui validação de CPF, CNPJ, email e telefone
- ✅ **TypeScript**: Totalmente tipado para melhor experiência de desenvolvimento

## 🚀 Instalação

Simplesmente copie o arquivo `pix-payload-generator.ts` para seu projeto. Não há dependências externas.

## 📖 Uso Básico

### Importação

```typescript
import { 
  generatePixPayload, 
  createPixPayload, 
  PixPayloadGenerator,
  PixPayloadConfig 
} from './pix-payload-generator';
```

### Exemplo Simples

```typescript
// Usando a função de conveniência
const payload = createPixPayload(
  'CPF',                    // Tipo da chave
  '12345678909',           // Chave PIX
  'João Silva',            // Nome do beneficiário
  'São Paulo',             // Cidade do beneficiário
  100.50,                  // Valor (opcional)
  'PAG001',                // Identificação (opcional)
  'Pagamento de serviços'  // Descrição (opcional)
);

console.log(payload);
// Resultado: 00020101021126...
```

### Exemplo com Configuração Completa

```typescript
const config: PixPayloadConfig = {
  keyType: 'EMAIL',
  key: '<EMAIL>',
  nameReceiver: 'Maria Santos',
  cityReceiver: 'Rio de Janeiro',
  amount: 25.00,
  zipcodeReceiver: '20040020',
  identification: 'TXN001',
  description: 'Transferência PIX',
  singleTransaction: false
};

const payload = generatePixPayload(config);
```

## 🔧 API Reference

### Tipos de Chave PIX Suportados

| Tipo | Descrição | Exemplo |
|------|-----------|---------|
| `CPF` | Cadastro de Pessoa Física | `'12345678909'` |
| `CNPJ` | Cadastro Nacional da Pessoa Jurídica | `'11222333000181'` |
| `EMAIL` | Endereço de email | `'<EMAIL>'` |
| `PHONE` | Número de telefone | `'11999887766'` |
| `RANDOM` | Chave aleatória | `'b5fe1edc-d108-410f-b966-eccaaca75e4f'` |

### Interface PixPayloadConfig

```typescript
interface PixPayloadConfig {
  keyType: 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE' | 'RANDOM';
  key: string;                    // Valor da chave PIX
  nameReceiver: string;           // Nome do beneficiário (máx 25 chars)
  cityReceiver: string;           // Cidade do beneficiário (máx 15 chars)
  amount?: number;                // Valor da transação (opcional)
  zipcodeReceiver?: string;       // CEP do beneficiário (opcional)
  identification?: string;        // ID da transação (opcional)
  description?: string;           // Descrição da transação (opcional)
  singleTransaction?: boolean;    // Transação única (opcional)
  dynamicUrl?: string;           // URL para PIX dinâmico (opcional)
}
```

### Funções Principais

#### `generatePixPayload(config: PixPayloadConfig): string`

Gera o payload PIX completo baseado na configuração fornecida.

#### `createPixPayload(...params): string`

Função de conveniência para gerar payload com parâmetros individuais.

#### `PixPayloadGenerator.generatePayload(config: PixPayloadConfig): string`

Método estático da classe principal para geração de payload.

## ✅ Validações Implementadas

### Validação de Chaves PIX

- **CPF**: Validação completa com dígitos verificadores
- **CNPJ**: Validação completa com dígitos verificadores
- **Email**: Validação de formato básico
- **Telefone**: Validação de formato internacional
- **Chave Aleatória**: Aceita qualquer string

### Validação de Campos

- **Nome do beneficiário**: Máximo 25 caracteres
- **Cidade do beneficiário**: Máximo 15 caracteres
- **Valor**: Máximo 13 caracteres (incluindo decimais)
- **Formatação de texto**: Remove acentos e caracteres especiais

## 🎯 Exemplos de Uso

### PIX com Valor Fixo

```typescript
const pixComValor = generatePixPayload({
  keyType: 'PHONE',
  key: '11999887766',
  nameReceiver: 'Loja ABC',
  cityReceiver: 'São Paulo',
  amount: 50.00,
  description: 'Compra de produto'
});
```

### PIX com Valor Aberto

```typescript
const pixValorAberto = generatePixPayload({
  keyType: 'EMAIL',
  key: '<EMAIL>',
  nameReceiver: 'ONG Exemplo',
  cityReceiver: 'Brasília',
  amount: 0, // ou omitir o campo
  description: 'Doação livre'
});
```

### PIX Dinâmico

```typescript
const pixDinamico = generatePixPayload({
  keyType: 'RANDOM', // Será ignorado
  key: '',           // Será ignorado
  dynamicUrl: 'psp.exemplo.com/pagamento/123',
  nameReceiver: 'Empresa XYZ',
  cityReceiver: 'Rio de Janeiro',
  singleTransaction: true
});
```

## 🛡️ Tratamento de Erros

O módulo lança erros descritivos para facilitar o debug:

```typescript
try {
  const payload = generatePixPayload({
    keyType: 'CPF',
    key: '12345678900', // CPF inválido
    nameReceiver: 'Test',
    cityReceiver: 'City'
  });
} catch (error) {
  console.error('Erro:', error.message);
  // Saída: "Erro: CPF inválido."
}
```

### Tipos de Erro Comuns

- `"É necessário fornecer uma chave PIX ou URL dinâmica."`
- `"Nome do beneficiário é obrigatório."`
- `"Cidade do beneficiário é obrigatória."`
- `"CPF inválido."` / `"CNPJ inválido."` / `"Email inválido."` / `"Telefone inválido."`
- `"O nome do beneficiário deve ter no máximo 25 caracteres."`
- `"A cidade do beneficiário deve ter no máximo 15 caracteres."`

## 🔍 Estrutura do Payload PIX

O payload gerado segue o padrão EMV QR Code:

```
00020101021126...6304XXXX
│   │   │   │      │   │
│   │   │   │      │   └── CRC16 (4 dígitos)
│   │   │   │      └────── Campo CRC (ID 63)
│   │   │   └─────────── Informações da conta (ID 26)
│   │   └─────────────── Método de iniciação (ID 01)
│   └─────────────────── Formato do payload (ID 00)
└─────────────────────── Indicador de formato (sempre "00")
```

## 🧪 Testes

Para testar o módulo, execute o arquivo de exemplo:

```bash
npx ts-node pix-payload-example.ts
```

## 📝 Notas Importantes

1. **Compatibilidade**: Este módulo gera payloads compatíveis com o padrão PIX do Banco Central do Brasil
2. **Segurança**: Todas as validações são realizadas localmente, sem envio de dados externos
3. **Performance**: Módulo leve e otimizado para uso em produção
4. **Manutenibilidade**: Código bem documentado e estruturado para fácil manutenção

## 🤝 Contribuição

Este módulo foi extraído de um projeto maior e pode ser usado independentemente. Para melhorias ou correções, considere:

1. Adicionar novos tipos de validação
2. Melhorar mensagens de erro
3. Adicionar suporte a novos campos PIX
4. Otimizar performance

## 📄 Licença

Este código pode ser usado livremente em projetos pessoais e comerciais.
