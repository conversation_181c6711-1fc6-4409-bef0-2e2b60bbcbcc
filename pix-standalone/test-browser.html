<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Teste Simples PIX</title>
  </head>
  <body>
    <h1>Teste Simples PIX Payload Generator</h1>
    <div id="result"></div>

    <script type="module">
      try {
        console.log('🔍 Verificando suporte a TextEncoder...');
        if (typeof TextEncoder === 'undefined') {
          throw new Error('TextEncoder não está disponível neste navegador');
        }
        console.log('✅ TextEncoder disponível');

        console.log('📦 Tentando importar módulo PIX...');

        const { generatePixPayload } = await import(
          './pix-payload-generator.js'
        );

        console.log('✅ Módulo importado com sucesso!');

        // Teste com os dados do erro
        const config = {
          keyType: 'CPF',
          key: '28304743841',
          nameReceiver: 'Wellington',
          cityReceiver: 'Franca',
          amount: 1.45,
        };

        console.log('📋 Configuração:', config);

        const payload = generatePixPayload(config);

        console.log('🎉 Payload gerado:', payload);

        document.getElementById('result').innerHTML = `
                <h2>✅ Sucesso!</h2>
                <p><strong>Payload:</strong></p>
                <pre style="background: #f0f0f0; padding: 10px; word-break: break-all;">${payload}</pre>
                <p><strong>Tamanho:</strong> ${payload.length} caracteres</p>
                <p><strong>Inicia corretamente:</strong> ${payload.startsWith('00020101') ? '✅ Sim' : '❌ Não'}</p>
                <p><strong>Termina com CRC:</strong> ${/[0-9A-F]{4}$/.test(payload) ? '✅ Sim' : '❌ Não'}</p>
            `;
      } catch (error) {
        console.error('❌ Erro:', error);
        document.getElementById('result').innerHTML = `
                <h2>❌ Erro</h2>
                <p><strong>Mensagem:</strong> ${error.message}</p>
                <p><strong>Stack:</strong></p>
                <pre style="background: #ffe6e6; padding: 10px; font-size: 12px;">${error.stack}</pre>
            `;
      }
    </script>
  </body>
</html>
