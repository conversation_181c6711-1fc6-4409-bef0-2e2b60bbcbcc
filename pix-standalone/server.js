/**
 * Servidor HTTP simples para servir os arquivos do PIX Payload Generator
 * Execute com: node server.js
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = process.env.PORT || 9876;

// MIME types para diferentes extensões
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
};

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Parse da URL
  let filePath = '.' + req.url;
  if (filePath === './') {
    filePath = './index.html';
  }

  // Obter extensão do arquivo
  const extname = String(path.extname(filePath)).toLowerCase();
  const mimeType = mimeTypes[extname] || 'application/octet-stream';

  // Ler e servir o arquivo
  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        // Arquivo não encontrado
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>404 - Arquivo não encontrado</title>
                        <style>
                            body { 
                                font-family: Arial, sans-serif; 
                                text-align: center; 
                                padding: 50px;
                                background: #f5f5f5;
                            }
                            .error-container {
                                background: white;
                                padding: 40px;
                                border-radius: 10px;
                                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                                display: inline-block;
                            }
                            h1 { color: #e74c3c; }
                            a { color: #3498db; text-decoration: none; }
                            a:hover { text-decoration: underline; }
                        </style>
                    </head>
                    <body>
                        <div class="error-container">
                            <h1>404 - Arquivo não encontrado</h1>
                            <p>O arquivo <code>${req.url}</code> não foi encontrado.</p>
                            <p><a href="/">← Voltar para a página inicial</a></p>
                        </div>
                    </body>
                    </html>
                `);
      } else {
        // Erro interno do servidor
        res.writeHead(500);
        res.end(`Erro interno do servidor: ${error.code}`);
      }
    } else {
      // Sucesso - servir o arquivo
      res.writeHead(200, {
        'Content-Type': mimeType,
        'Cache-Control': 'no-cache', // Evitar cache durante desenvolvimento
      });
      res.end(content, 'utf-8');
    }
  });
});

server.listen(PORT, () => {
  console.log('🚀 PIX Payload Generator Server iniciado!');
  console.log(`📍 Servidor rodando em: http://localhost:${PORT}`);
  console.log(`📁 Servindo arquivos do diretório: ${__dirname}`);
  console.log('');
  console.log('📋 Arquivos disponíveis:');
  console.log(
    `   • http://localhost:${PORT}/ - Página principal com formulário`
  );
  console.log(
    `   • http://localhost:${PORT}/PIX-PAYLOAD-README.md - Documentação`
  );
  console.log(
    `   • http://localhost:${PORT}/pix-payload-generator.js - Módulo PIX`
  );
  console.log('');
  console.log('⏹️  Para parar o servidor, pressione Ctrl+C');
  console.log('');
});

// Tratamento de encerramento gracioso
process.on('SIGINT', () => {
  console.log('\n🛑 Encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado com sucesso!');
    process.exit(0);
  });
});

// Tratamento de erros não capturados
process.on('uncaughtException', (err) => {
  console.error('❌ Erro não capturado:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promise rejeitada não tratada:', reason);
  process.exit(1);
});
