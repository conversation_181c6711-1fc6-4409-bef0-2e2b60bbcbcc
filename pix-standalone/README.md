# PIX Payload Generator - Standalone

Módulo independente para geração de payload PIX com interface web para testes.

## 📁 Estrutura do Diretório

```
pix-standalone/
├── index.html                    # Interface web com formulário de teste
├── server.js                     # Servidor HTTP simples para desenvolvimento
├── pix-payload-generator.ts      # <PERSON><PERSON><PERSON><PERSON> principal (TypeScript)
├── pix-payload-generator.js      # <PERSON>ó<PERSON>lo principal (JavaScript compilado)
├── pix-payload-example.ts        # Exemplos de uso (TypeScript)
├── pix-payload-example.js        # Exemplos de uso (JavaScript compilado)
├── test-pix-payload.js           # Testes funcionais
├── pix-payload-generator.test.ts # Testes em TypeScript
├── PIX-PAYLOAD-README.md         # Documentação técnica completa
├── EXTRAÇÃO-PIX-RESUMO.md        # Resumo da extração realizada
└── README.md                     # Este arquivo
```

## 🚀 Como Usar

### Opção 1: Interface Web (Recomendado para Testes)

1. **Inicie o servidor local:**
   ```bash
   cd pix-standalone
   node server.js
   ```

2. **Abra no navegador:**
   ```
   http://localhost:3000
   ```

3. **Use o formulário para testar:**
   - Preencha os campos obrigatórios (tipo de chave, chave PIX, nome, cidade)
   - Preencha os campos opcionais conforme necessário
   - Clique em "Gerar Payload PIX"
   - Copie o payload gerado

### Opção 2: Uso Programático

#### Em Node.js:
```javascript
const { generatePixPayload } = require('./pix-payload-generator.js');

const payload = generatePixPayload({
  keyType: 'CPF',
  key: '12345678909',
  nameReceiver: 'João Silva',
  cityReceiver: 'São Paulo',
  amount: 100.50,
  description: 'Pagamento de serviços'
});

console.log(payload);
```

#### Em TypeScript:
```typescript
import { generatePixPayload } from './pix-payload-generator';

const payload = generatePixPayload({
  keyType: 'EMAIL',
  key: '<EMAIL>',
  nameReceiver: 'Maria Santos',
  cityReceiver: 'Rio de Janeiro',
  amount: 25.00
});
```

### Opção 3: Executar Testes

```bash
# Testes simples em JavaScript
node test-pix-payload.js

# Exemplos de uso
node pix-payload-example.js
```

## 🎯 Funcionalidades da Interface Web

### Formulário Inteligente
- **Validação em tempo real** dos campos
- **Placeholders dinâmicos** baseados no tipo de chave
- **Ajuda contextual** para cada campo
- **Responsivo** para dispositivos móveis

### Tipos de Chave Suportados
- **CPF**: Validação completa com dígitos verificadores
- **CNPJ**: Validação completa com dígitos verificadores
- **Email**: Validação de formato
- **Telefone**: Formatação automática com +55
- **Chave Aleatória**: UUID ou string personalizada

### Campos do Formulário
- ✅ **Tipo de Chave PIX** (obrigatório)
- ✅ **Chave PIX** (obrigatório)
- ✅ **Nome do Beneficiário** (obrigatório, máx 25 chars)
- ✅ **Cidade do Beneficiário** (obrigatório, máx 15 chars)
- ⚪ **Valor da Transação** (opcional, 0 = valor aberto)
- ⚪ **CEP do Beneficiário** (opcional)
- ⚪ **Identificador da Transação** (opcional)
- ⚪ **Descrição da Transação** (opcional)
- ⚪ **Transação Única** (checkbox para PIX dinâmico)

### Resultado
- **Payload completo** em formato copiável
- **Informações do payload** (tamanho, tipo, valor, etc.)
- **Botão de cópia** para área de transferência
- **Validação visual** de sucesso/erro

## 🛠️ Desenvolvimento

### Pré-requisitos
- Node.js (para servidor local)
- Navegador moderno (suporte a ES6 modules)

### Estrutura do Código

#### Módulo Principal (`pix-payload-generator.ts/js`)
- Classe `PixPayloadGenerator`
- Funções de conveniência `generatePixPayload()` e `createPixPayload()`
- Validações completas de CPF, CNPJ, email, telefone
- Formatação de texto (remoção de acentos)
- Cálculo de CRC16-CCITT-FALSE

#### Interface Web (`index.html`)
- HTML5 semântico
- CSS3 com design responsivo
- JavaScript ES6 com modules
- Integração direta com o módulo PIX

#### Servidor (`server.js`)
- Servidor HTTP simples
- Suporte a MIME types
- Páginas de erro personalizadas
- Logs de acesso

## 🧪 Exemplos de Teste

### PIX com CPF
```
Tipo: CPF
Chave: 12345678909
Nome: João Silva
Cidade: São Paulo
Valor: 100.50
```

### PIX com Email (Valor Aberto)
```
Tipo: EMAIL
Chave: <EMAIL>
Nome: Maria Santos
Cidade: Rio de Janeiro
Valor: (vazio para valor aberto)
```

### PIX Dinâmico
```
Tipo: RANDOM
Chave: b5fe1edc-d108-410f-b966-eccaaca75e4f
Nome: Loja Virtual
Cidade: São Paulo
Transação Única: ✓ marcado
```

## 📋 Validações Implementadas

### Automáticas
- Tamanho máximo de campos
- Formato de chaves PIX
- Campos obrigatórios
- Valores numéricos

### Manuais (via interface)
- CPF com dígitos verificadores
- CNPJ com dígitos verificadores
- Email com formato válido
- Telefone com formato brasileiro

## 🎨 Interface

A interface foi projetada com:
- **Design moderno** com gradientes e sombras
- **Cores do PIX** (verde) como tema principal
- **Feedback visual** para ações do usuário
- **Responsividade** para diferentes tamanhos de tela
- **Acessibilidade** com labels e ajuda contextual

## 🔧 Personalização

### Modificar Estilos
Edite a seção `<style>` no arquivo `index.html` para personalizar:
- Cores do tema
- Layout dos campos
- Animações e transições
- Responsividade

### Adicionar Validações
Modifique o arquivo `pix-payload-generator.ts` para:
- Adicionar novos tipos de chave
- Implementar validações customizadas
- Modificar formatação de texto

### Estender Funcionalidades
- Adicione novos campos no formulário
- Implemente salvamento de configurações
- Integre com APIs de QR Code
- Adicione histórico de payloads gerados

## 📄 Licença

Este código pode ser usado livremente em projetos pessoais e comerciais.

## 🤝 Contribuição

Para melhorias ou correções:
1. Teste as modificações na interface web
2. Execute os testes automatizados
3. Verifique compatibilidade com padrões PIX
4. Documente as alterações
