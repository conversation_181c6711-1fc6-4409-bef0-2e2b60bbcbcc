/**
 * Teste do módulo PIX em Node.js
 */

// Como o arquivo está em ES6 modules, vamos usar import dinâmico
async function testPix() {
    try {
        console.log('🧪 Testando módulo PIX...');
        
        // Importar o módulo
        const { generatePixPayload } = await import('./pix-payload-generator.js');
        
        console.log('✅ Módulo importado com sucesso!');
        
        // Teste com os dados do erro
        const config = {
            keyType: 'CPF',
            key: '28304743841',
            nameReceiver: 'Wellington',
            cityReceiver: 'Franca',
            amount: 1.45
        };
        
        console.log('📋 Configuração:', config);
        
        const payload = generatePixPayload(config);
        
        console.log('🎉 Payload gerado com sucesso!');
        console.log('📄 Payload:', payload);
        console.log('📏 Tamanho:', payload.length, 'caracteres');
        console.log('✅ Inicia corretamente:', payload.startsWith('00020101') ? 'Sim' : 'Não');
        console.log('✅ Termina com CRC:', /[0-9A-F]{4}$/.test(payload) ? 'Sim' : 'Não');
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
        console.error('📍 Stack:', error.stack);
    }
}

// Executar teste
testPix();
