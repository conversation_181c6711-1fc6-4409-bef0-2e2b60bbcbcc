<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PIX Payload Generator - Teste</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #32CD32 0%, #228B22 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.95em;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #32CD32;
            background: white;
            box-shadow: 0 0 0 3px rgba(50, 205, 50, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #32CD32 0%, #228B22 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(50, 205, 50, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result-container {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #32CD32;
        }

        .result-container h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .payload-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            word-break: break-all;
            margin-bottom: 15px;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4a5568;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .copy-btn:hover {
            background: #2d3748;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        .info-item strong {
            color: #32CD32;
            display: block;
            margin-bottom: 5px;
        }

        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #e53e3e;
            margin-top: 20px;
        }

        .success {
            background: #c6f6d5;
            color: #22543d;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #38a169;
            margin-top: 20px;
        }

        .help-text {
            font-size: 0.85em;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 PIX Payload Generator</h1>
            <p>Gerador independente de payload PIX compatível com padrões do Banco Central</p>
        </div>

        <div class="form-container">
            <form id="pixForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="keyType">Tipo de Chave PIX *</label>
                        <select id="keyType" name="keyType" required>
                            <option value="">Selecione o tipo</option>
                            <option value="CPF">CPF</option>
                            <option value="CNPJ">CNPJ</option>
                            <option value="EMAIL">Email</option>
                            <option value="PHONE">Telefone</option>
                            <option value="RANDOM">Chave Aleatória</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="key">Chave PIX *</label>
                        <input type="text" id="key" name="key" required placeholder="Digite a chave PIX">
                        <div class="help-text" id="keyHelp">Selecione o tipo de chave primeiro</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="nameReceiver">Nome do Beneficiário *</label>
                        <input type="text" id="nameReceiver" name="nameReceiver" required 
                               placeholder="Nome completo" maxlength="25">
                        <div class="help-text">Máximo 25 caracteres</div>
                    </div>
                    <div class="form-group">
                        <label for="cityReceiver">Cidade do Beneficiário *</label>
                        <input type="text" id="cityReceiver" name="cityReceiver" required 
                               placeholder="Cidade" maxlength="15">
                        <div class="help-text">Máximo 15 caracteres</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="amount">Valor da Transação</label>
                        <input type="number" id="amount" name="amount" step="0.01" min="0" 
                               placeholder="0.00">
                        <div class="help-text">Deixe vazio ou 0 para valor aberto</div>
                    </div>
                    <div class="form-group">
                        <label for="zipcodeReceiver">CEP do Beneficiário</label>
                        <input type="text" id="zipcodeReceiver" name="zipcodeReceiver" 
                               placeholder="00000000" maxlength="8">
                        <div class="help-text">Opcional - apenas números</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="identification">Identificador da Transação</label>
                        <input type="text" id="identification" name="identification" 
                               placeholder="TXN001" maxlength="25">
                        <div class="help-text">Opcional - referência interna</div>
                    </div>
                    <div class="form-group">
                        <label for="description">Descrição da Transação</label>
                        <input type="text" id="description" name="description" 
                               placeholder="Descrição do pagamento" maxlength="72">
                        <div class="help-text">Opcional - descrição do pagamento</div>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="singleTransaction" name="singleTransaction">
                        Transação única (PIX dinâmico)
                    </label>
                    <div class="help-text">Marque para PIX de uso único</div>
                </div>

                <button type="submit" class="btn">🚀 Gerar Payload PIX</button>
            </form>

            <div id="result" style="display: none;"></div>
        </div>
    </div>

    <script type="module">
        // Importar o módulo PIX
        import { generatePixPayload } from './pix-payload-generator.js';

        // Elementos do DOM
        const form = document.getElementById('pixForm');
        const keyTypeSelect = document.getElementById('keyType');
        const keyInput = document.getElementById('key');
        const keyHelp = document.getElementById('keyHelp');
        const resultDiv = document.getElementById('result');

        // Atualizar placeholder e ajuda baseado no tipo de chave
        keyTypeSelect.addEventListener('change', function() {
            const keyType = this.value;
            const placeholders = {
                'CPF': { placeholder: '***********', help: 'Digite apenas números (11 dígitos)' },
                'CNPJ': { placeholder: '11222333000181', help: 'Digite apenas números (14 dígitos)' },
                'EMAIL': { placeholder: '<EMAIL>', help: 'Digite um email válido' },
                'PHONE': { placeholder: '***********', help: 'Digite apenas números (DDD + número)' },
                'RANDOM': { placeholder: 'b5fe1edc-d108-410f-b966-eccaaca75e4f', help: 'Chave aleatória (UUID, etc.)' }
            };

            if (placeholders[keyType]) {
                keyInput.placeholder = placeholders[keyType].placeholder;
                keyHelp.textContent = placeholders[keyType].help;
            } else {
                keyInput.placeholder = 'Digite a chave PIX';
                keyHelp.textContent = 'Selecione o tipo de chave primeiro';
            }
        });

        // Submissão do formulário
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            try {
                // Coletar dados do formulário
                const formData = new FormData(form);
                const config = {
                    keyType: formData.get('keyType'),
                    key: formData.get('key'),
                    nameReceiver: formData.get('nameReceiver'),
                    cityReceiver: formData.get('cityReceiver'),
                    amount: formData.get('amount') ? parseFloat(formData.get('amount')) : undefined,
                    zipcodeReceiver: formData.get('zipcodeReceiver') || undefined,
                    identification: formData.get('identification') || undefined,
                    description: formData.get('description') || undefined,
                    singleTransaction: formData.get('singleTransaction') === 'on'
                };

                // Gerar payload
                const payload = generatePixPayload(config);
                
                // Mostrar resultado
                showResult(payload, config);
                
            } catch (error) {
                showError(error.message);
            }
        });

        function showResult(payload, config) {
            const resultHTML = `
                <div class="result-container">
                    <h3>✅ Payload PIX Gerado com Sucesso!</h3>
                    
                    <div class="payload-output">
                        <button class="copy-btn" onclick="copyToClipboard('${payload}')">Copiar</button>
                        ${payload}
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Tamanho:</strong>
                            ${payload.length} caracteres
                        </div>
                        <div class="info-item">
                            <strong>Tipo de Chave:</strong>
                            ${config.keyType}
                        </div>
                        <div class="info-item">
                            <strong>Valor:</strong>
                            ${config.amount ? `R$ ${config.amount.toFixed(2)}` : 'Valor aberto'}
                        </div>
                        <div class="info-item">
                            <strong>Transação:</strong>
                            ${config.singleTransaction ? 'Única' : 'Reutilizável'}
                        </div>
                    </div>
                    
                    <div class="success">
                        <strong>Payload válido!</strong> Este código pode ser usado para gerar QR Code PIX ou processamento direto.
                    </div>
                </div>
            `;
            
            resultDiv.innerHTML = resultHTML;
            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function showError(message) {
            const errorHTML = `
                <div class="error">
                    <strong>❌ Erro na geração do payload:</strong><br>
                    ${message}
                </div>
            `;
            
            resultDiv.innerHTML = errorHTML;
            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // Função global para copiar payload
        window.copyToClipboard = function(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Feedback visual
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = 'Copiado!';
                btn.style.background = '#38a169';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#4a5568';
                }, 2000);
            }).catch(function(err) {
                console.error('Erro ao copiar: ', err);
                alert('Erro ao copiar para área de transferência');
            });
        };
    </script>
</body>
</html>
